<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.skyworth.smarthome_tv"
    android:sharedUserId="android.uid.system">
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取网络状态改变的权限 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 允许应用程序改变网络状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 允许应用程序改变WIFI连接状态 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 允许应用程序完全使用网络 -->
    <uses-permission android:name="android.permission.BLUETOOTH" /> <!-- 获取蓝牙的权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /><!-- 开启/禁用屏保权限 -->
    <uses-permission android:name="com.tianci.user.permission.READ_CONTENT" />
    <uses-sdk tools:overrideLibrary="com.ccos.plugin" />
    <application
        android:name=".SmartHomeApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="n">
        <activity
            android:name=".HomeActivity"
            android:screenOrientation="landscape"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.smarthome.action.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".MainPluginTestActivity">
            <intent-filter>
                <action android:name="com.smarthome.action.PLUGIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="HOMEPAGE_SERVER"
            android:value="${HOMEPAGE_SERVER_VALUE}" />
        <meta-data
            android:name="APPSTORE_SERVER"
            android:value="${APPSTORE_SERVER_VALUE}" />
    </application>

</manifest>