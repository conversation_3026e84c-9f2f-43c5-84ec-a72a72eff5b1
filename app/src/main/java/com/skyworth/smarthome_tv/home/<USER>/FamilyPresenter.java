package com.skyworth.smarthome_tv.home.family;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome_tv.common.model.FamilyInfoData;
import com.skyworth.smarthome_tv.home.family.data.FamilyStatusData;
import com.skyworth.smarthome_tv.home.family.data.OnlineDeviceData;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.skyworth.smarthome.common.bean.CityBean;
import com.skyworth.smarthome.common.bean.WeathBean;
import com.skyworth.smarthome.common.event.AccountChangeEvent;
import com.skyworth.smarthome.common.http.SmartHomeHttpService;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.service.push.local.IHandlerPush;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.FamilyStatusBean;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/4
 */
public class FamilyPresenter {

    private final static String TAG = "FamilyPresenter";
    private FamilyView familyView;
    private List<FamilyBean> mFamilyList = new ArrayList<>();
    private FamilyInfoData mFamilyInfoData;
    private OnlineDeviceData mOnlineDeviceData = new OnlineDeviceData();
    private String mCurrentCity = "";
    private String mCurrentFamilyInfo = "";//给雷倍一打补丁1（相同数据同一时间会推多次，此处做一下拦截）

    private IHandlerPush.IPushListener iPushListener = new IHandlerPush.IPushListener() {

        @Override
        public void onArrive(AppConstants.SSE_PUSH event, String data) {
            CCLog.i(TAG, "onArrive: ----------event:" + event);
            if (EmptyUtils.isEmpty(data) || "null".equals(data)) {
                CCLog.i(TAG, "onHttpArrive: event:" + event + " -- data is null !!!");
                return;
            }
            switch (event) {
                case FAMILY_LIST://家庭列表推送
                    refreshFamilyList(data);
                    break;
                case CURRENT_FAMILY:
                    if (EmptyUtils.isNotEmpty(mCurrentFamilyInfo) && mCurrentFamilyInfo.equals(data)) {
                        CCLog.i(TAG, "onHttpArrive: event:" + event + " -- same data, return...");
                        return;
                    }
                    refreshCurrentFamily(data);
                    break;
                case HOME_STATUS_INFO://家庭信息推送
                    refreshFamilyStatus(data);
                    break;
                case DEVICE_NOTIFY://设备消息通知
                    break;
                default:
                    break;
            }
        }
    };

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(AccountChangeEvent event) {
        mCurrentFamilyInfo = "";
        checkLogin();
        refreshFamilyUI("");
    }

    public FamilyPresenter(FamilyView familyView) {
        this.familyView = familyView;
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        DeviceDataPushUtil.getPush().regReceiver(iPushListener);
        checkLogin();
        refreshFamilyUI("");
    }

    public void bindServiceFail() {
        refreshFamilyUI("");
    }

    private void checkLogin() {
        if (!IAppAccountManager.INSTANCE.hasLogin()) {
            CCLog.i(TAG, "checkLogin(): hasLogin: false");
            loadCityAndWeatherInfo();
        }
    }

    private void refreshCurrentFamily(String data) {
        mCurrentFamilyInfo = data;
        FamilyBean currentFamily = JSONObject.parseObject(data, FamilyBean.class);
        if (EmptyUtils.isNotEmpty(currentFamily)) {
            if (EmptyUtils.isNotEmpty(currentFamily.city) && !currentFamily.city.equals("undefined")) {
                mCurrentCity = currentFamily.city;
            } else {
                mCurrentCity = "";
            }
            mFamilyInfoData = new FamilyInfoData();
            mFamilyInfoData.current = currentFamily;
            if (EmptyUtils.isNotEmpty(mFamilyList)) {
                mFamilyInfoData.familyList.clear();
                mFamilyInfoData.familyList.addAll(mFamilyList);
            } else {
                FamilyBean family = new FamilyBean();
                family.family_id = mFamilyInfoData.current.family_id;
                family.family_name = mFamilyInfoData.current.family_name;
                mFamilyInfoData.familyList.add(family);
            }
            //刷新页面
            CCLog.i(TAG, "refreshCurrentFamily: ");
            refreshFamilyUI(mFamilyInfoData.current.family_name);
        }
    }

    private void refreshFamilyUI(final String familyName) {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                familyView.refreshFamilyUI(familyName);
            }
        });
    }

    private void refreshFamilyList(String data) {
        List<FamilyBean> familyList = JSONObject.parseArray(data, FamilyBean.class);
        if (EmptyUtils.isNotEmpty(familyList)) {
            mFamilyList.clear();
            mFamilyList.addAll(familyList);
            if (mFamilyInfoData != null) {
                //表示页面已经刷新
                mFamilyInfoData.familyList.clear();
                mFamilyInfoData.familyList.addAll(familyList);
                CCLog.i(TAG, "refreshFamilyList: update family list data.");
            } else {
                CCLog.i(TAG, "refreshFamilyList: family list data is ready...");
            }
        }
    }

    private void refreshFamilyStatus(String data) {
        AppData.getInstance().setHomeStatusData(data);
        FamilyStatusBean familyStatusBean = JSONObject.parseObject(data, FamilyStatusBean.class);
        if (EmptyUtils.isNotEmpty(familyStatusBean)) {
            mOnlineDeviceData.setOnlineCount(familyStatusBean.running_device_count);
            StringBuilder builder = new StringBuilder();
            boolean needGetWeather = false;
            if (EmptyUtils.isNotEmpty(familyStatusBean.temperature) || EmptyUtils.isNotEmpty(familyStatusBean.humidity)) {
                String airQuality = familyStatusBean.air_quality;
                if (EmptyUtils.isNotEmpty(familyStatusBean.temperature)) {
                    builder.append("温度" + familyStatusBean.temperature);
                    if (EmptyUtils.isNotEmpty(familyStatusBean.humidity)) {
                        builder.append("   " + "湿度" + familyStatusBean.humidity);
                    }
                } else if (EmptyUtils.isNotEmpty(familyStatusBean.humidity)) {
                    builder.append("湿度" + familyStatusBean.humidity);
                }
                if (EmptyUtils.isNotEmpty(airQuality)) {
                    builder.append("  空气质量 ").append(airQuality);
                }
            } else {
                needGetWeather = true;
            }
            FamilyStatusData familyStatusData = new FamilyStatusData();
            familyStatusData.weatherInfoTip = builder.toString();
            familyStatusData.onlineTip = getOnlineTip(mOnlineDeviceData.getOnlineCount(), mOnlineDeviceData.isVirtual());
            if (needGetWeather) {
                getWeatherInfo(mCurrentCity);
            }else{
                refreshStatusUI(familyStatusData);
            }
        }
    }

    private void refreshStatusUI(final FamilyStatusData familyStatusData) {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                familyView.refreshStatusUI(familyStatusData);
            }
        });
    }

    private void loadCityAndWeatherInfo() {



        HttpServiceManager.Companion.call(SmartHomeHttpService.SERVICE.getCityInfo(), new Function1<HttpServiceManager.ERROR, Unit>() {
            @Override
            public Unit invoke(HttpServiceManager.ERROR error) {
                CCLog.i(TAG, "getCityInfo onError: " + error.getMsg());
                return Unit.INSTANCE;
            }
        }, new Function1<SmartBaseData<CityBean>, Unit>() {
            @Override
            public Unit invoke(SmartBaseData<CityBean> cityBeanSmartBaseData) {
                mCurrentCity = cityBeanSmartBaseData.data.city_name;
                CCLog.i(TAG, "getCityInfo onSuccess: " + mCurrentCity);
                getWeatherInfo(mCurrentCity);
                return Unit.INSTANCE;
            }
        });
    }

    private void getWeatherInfo(String city) {
        if (EmptyUtils.isEmpty(city)) {
            loadCityAndWeatherInfo();
            return;
        }


        HttpServiceManager.Companion.call(SmartHomeHttpService.SERVICE.getWeatherInfo(city), new Function1<HttpServiceManager.ERROR, Unit>() {
            @Override
            public Unit invoke(HttpServiceManager.ERROR error) {
                CCLog.i(TAG, "getWeatherInfo onError: " + error.getMsg());
                FamilyStatusData familyStatusData = new FamilyStatusData();
//                familyStatusData.weatherInfoTip = "温度0°C  |  湿度0%";
                familyStatusData.onlineTip = getOnlineTip(mOnlineDeviceData.getOnlineCount(), mOnlineDeviceData.isVirtual());
                refreshStatusUI(familyStatusData);
                return Unit.INSTANCE;
            }
        }, new Function1<SmartBaseData<WeathBean>, Unit>() {
            @Override
            public Unit invoke(SmartBaseData<WeathBean> cityBeanSmartBaseData) {
                CCLog.i(TAG, "getWeatherInfo onSuccess: ");
                WeathBean bean = cityBeanSmartBaseData.data;
                FamilyStatusData familyStatusData = new FamilyStatusData();
                if (EmptyUtils.isNotEmpty(bean)) {
                    familyStatusData.weatherInfoTip = bean.city + "   " + bean.min_temp + "°~" + bean.max_temp + "°   " + bean.weather + "   " + bean.wind;
                } else {
//                    familyStatusData.weatherInfoTip = "温度0°C  |  湿度0%";
                }
                familyStatusData.onlineTip = getOnlineTip(mOnlineDeviceData.getOnlineCount(), mOnlineDeviceData.isVirtual());
                refreshStatusUI(familyStatusData);
                return Unit.INSTANCE;
            }
        });
    }

    private String getOnlineTip(int onlineCount, boolean isVirtual) {
        String onlineTip;
        if (onlineCount > 0) {
            if (isVirtual) {
                onlineTip = onlineCount + "个虚拟设备";
            } else {
                onlineTip = onlineCount + "个设备运行中";
            }
            return onlineTip;
        }
        return "";
    }


    public void destroy() {
        DeviceDataPushUtil.getPush().unRegReceiver(iPushListener);
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
