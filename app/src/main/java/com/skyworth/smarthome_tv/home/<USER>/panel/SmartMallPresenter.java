package com.skyworth.smarthome_tv.home.custom.panel;

import android.content.Context;
import android.view.View;

import com.coocaa.operate6_0.presenter.Presenter;
import com.coocaa.smartmall.MallApp;
import com.coocaa.smartmall.tabui.DiscoverTabMainPluglayoutV2;
import com.skyworth.smarthome.BuildConfig;
import com.skyworth.smarthome_tv.smarthomeplugininterface.IViewBoundaryCallback;

/**
 * @Description: 智家购物模块
 * @Author: wzh
 * @CreateDate: 2020/9/9
 */
public class SmartMallPresenter extends Presenter {

    private DiscoverTabMainPluglayoutV2 mMainPluglayoutV2;

    private IViewBoundaryCallback mBoundaryCallback = new IViewBoundaryCallback() {

        @Override
        public boolean onTopBoundary(View leaveView) {
            return mBoundaryListener.onTopBoundary(leaveView, getContainer(), mPosition);
        }

        @Override
        public boolean onDownBoundary(View leaveView) {
            return mBoundaryListener.onDownBoundary(leaveView, getContainer(), mPosition);
        }

        @Override
        public boolean onLeftBoundary(View leaveView) {
            return mBoundaryListener.onLeftBoundary(leaveView, getContainer(), mPosition);
        }

        @Override
        public boolean onRightBoundary(View leaveView) {
            return mBoundaryListener.onRightBoundary(leaveView, getContainer(), mPosition);
        }

        @Override
        public boolean onBackKey(View leaveView) {
            return false;
        }
    };

    public SmartMallPresenter(Context context) {
        super(context);
        MallApp.init(mContext, BuildConfig.DEBUG);
        mMainPluglayoutV2 = new DiscoverTabMainPluglayoutV2(context, mBoundaryCallback);
        mMainPluglayoutV2.createDiscoverTabPluginLayout();
    }

    @Override
    public View getView() {
        return mMainPluglayoutV2;
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onDestroy() {

    }
}
