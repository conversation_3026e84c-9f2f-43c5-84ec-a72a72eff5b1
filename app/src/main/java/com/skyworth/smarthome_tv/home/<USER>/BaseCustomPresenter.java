package com.skyworth.smarthome_tv.home.custom;

import android.content.Context;
import android.view.View;

import com.coocaa.operate6_0.presenter.Presenter;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public abstract class BaseCustomPresenter extends Presenter implements IBoundaryCallback {

    public BaseCustomPresenter(Context context) {
        super(context);
    }

    @Override
    public View getView() {
        return null;
    }

    @Override
    public boolean onTop(View leaveView) {
        return mBoundaryListener.onTopBoundary(leaveView, getContainer(), mPosition);
    }

    @Override
    public boolean onLeft(View leaveView) {
        return mBoundaryListener.onLeftBoundary(leaveView, getContainer(), mPosition);
    }

    @Override
    public boolean onRight(View leaveView) {
        return mBoundaryListener.onRightBoundary(leaveView, getContainer(), mPosition);
    }

    @Override
    public boolean onDown(View leaveView) {
        return mBoundaryListener.onDownBoundary(leaveView, getContainer(), mPosition);
    }

    @Override
    public boolean onBackKey(View leaveView) {
        return false;
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onDestroy() {

    }
}
