package com.skyworth.smarthome_tv.home.custom.panel;

import android.content.Context;

import com.skyworth.smarthome_tv.home.custom.BasePluginLayout;
import com.skyworth.smarthome_tv.home.custom.Custom;
import com.skyworth.smarthome_tv.home.custom.BasePluginPresenter;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/18
 */
public class PluginPanelPresenter extends BasePluginPresenter {

    public PluginPanelPresenter(Context context, String type) {
        super(context, type);
    }

    @Override
    protected void init() {
        PKGS.put(Custom.TYPE_VIDEO_CALL, Custom.PKG_VIDEO_CALL);
    }

    @Override
    protected BasePluginLayout getPluginLayout() {
        return new PluginPanelLayout(mContext);
    }

}
