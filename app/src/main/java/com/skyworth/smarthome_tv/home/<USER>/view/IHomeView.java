package com.skyworth.smarthome_tv.home.main.view;

import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.coocaa.operate6_0.model.AppendData;
import com.coocaa.operate6_0.model.Container;
import com.coocaa.operate6_0.presenter.LoadMorePresenter;
import com.skyworth.smarthome_tv.home.family.FamilyView;
import com.skyworth.smarthome_tv.home.main.presenter.IHomePresenter;
import com.skyworth.smarthome_tv.home.main.view.data.NavigationData;
import com.smarthome.common.utils.callback.LifecycleInterface;

import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/4
 */
public interface IHomeView extends LifecycleInterface {
    void create(Context context, IHomePresenter presenter);

    void onNewIntent(Intent intent);

    View getView();

    void refreshTabUI(List<NavigationData> datas);

    void createContentView(Container container, int tabIndex);

    FamilyView getFamilyView();

    boolean onKeyDown(int keyCode);

    interface ILayoutListener {
        void switchTab(int position);

        void loadNextPageData(String url, int id, AppendData appendData, LoadMorePresenter.LoadMoreCallback loadMoreCallback);

        boolean getFocus(int whichView, int from);

        void showTopView();

        void hideTopView();

        void onUserIconFocus(boolean hasFocus);
    }
}
