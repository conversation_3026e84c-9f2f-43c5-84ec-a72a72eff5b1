package com.skyworth.smarthome_tv.home.main.presenter;

import android.content.Context;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome_tv.home.custom.Custom;
import com.skyworth.smarthome.common.util.CommonUtil;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.AppManager;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.IUpdateListener;
import com.smarthome.common.utils.CCLog;

/**
 * @Description: 跨屏互动拉平逻辑
 * @Author: wzh
 * @CreateDate: 2020/9/15
 */
public class IotChannelPresenter {

    private final static String TAG = "IotChannelPresenter";
    private Context mContext;
    private boolean needReload = false;

    public IotChannelPresenter(Context context) {
        mContext = context;
    }

    public void start() {
        if (!CommonUtil.isNetConnected(mContext)) {
            needReload = true;
            log("start: network broken.");
            return;
        }
        needReload = false;
        FuncKt.ioThread(new Runnable() {
            @Override
            public void run() {
                try {
                    log("start check IotChannel.");
                    if (AppManager.checkUpdate(mContext, Custom.PKG_IOT_CHANNEL)) {
                        log("IotChannel update start.");
                        boolean ret = AppManager.downloadAndInstall(mContext, Custom.PKG_IOT_CHANNEL, iUpdateListener);
                        log("IotChannel update end. result:" + ret);
                    } else {
                        log("IotChannel version is new or network error.");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public void onNetChanged(boolean b) {
        log("onNetChanged connect:" + b + "---needReload:" + needReload);
        if (b && needReload) {
            start();
        }
    }

    private IUpdateListener iUpdateListener = new IUpdateListener() {
        @Override
        public void onDownloadStart() {
            log("onDownloadStart.");
        }

        @Override
        public void onDownloadProcess(int process) {
            log("onDownloadProcess:" + process);
        }

        @Override
        public void onDownloadEnd(boolean res) {
            log("onDownloadEnd:" + res);
        }

        @Override
        public void onInstallStart() {
            log("onInstallStart.");
        }

        @Override
        public void onInstallEnd(boolean res, String reason) {
            log("onInstallEnd:" + res + "  " + reason);
        }
    };

    private void log(String msg) {
        CCLog.i(TAG, msg);
    }

}
