package com.skyworth.smarthome_tv.home.main.view;

import android.content.Context;
import android.content.Intent;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.coocaa.app.core.utils.FuncKt;
import com.coocaa.operate6_0.model.AppendData;
import com.coocaa.operate6_0.model.Container;
import com.coocaa.operate6_0.presenter.LoadMorePresenter;
import com.skyworth.smarthome.common.util.DialogCommonUtil;
import com.skyworth.smarthome_tv.home.family.FamilyView;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.dialog.OfflineHelpWifiDialog;
import com.skyworth.smarthome.common.dialog.OfflineHelpZigbeeDialog;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome_tv.home.HomeUtil;
import com.skyworth.smarthome_tv.home.main.presenter.IHomePresenter;
import com.skyworth.smarthome_tv.home.main.view.data.NavigationData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.DeviceControlDialog;
import com.skyworth.smarthome_tv.pluginmanager.PluginManager;
import com.skyworth.ui.api.HorizontalScrollView;
import com.skyworth.util.Util;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static android.content.Intent.FLAG_ACTIVITY_NEW_TASK;

/**
 * @Description: 智慧家庭主页
 * @Author: wzh
 * @CreateDate: 2020/6/3
 */
public class HomeView extends FrameLayout implements IHomeView {

    private final static String TAG = "HomeView";
    private IHomePresenter mHomePresenter;
    private FamilyView mFamilyView;
    private NavigationView mNavigationView;
    private HorizontalScrollView mHScrollView;
    private LinearLayout mHScrollContentLayout;
    private TextView mTempBtn;
    private List<ContentView> mViewList = new ArrayList<>();
    private int mCurrentTabIndex = 0;
    private boolean mTabIsShow = true;
    private boolean mLayoutIsReady = false;
    private boolean mContentIsScrollEnd = true;
    private boolean mNeedSmoothScroll = true;
    private OfflineHelpWifiDialog mOfflineHelpWifiDialog;
    private OfflineHelpZigbeeDialog mOfflineHelpZigbeeDialog;
    private DeviceControlDialog mDeviceControlDialog;

    private int mContentTopMargin = Util.Div(225);
    public final static int FOCUS_TO_TOP = 0;
    public final static int FOCUS_TO_TAB = 1;
    public final static int FOCUS_TO_CONTENT = 2;
    public final static int FROM_TOP = 3;
    public final static int FROM_BUTTOM = 4;
    public final static int FROM_TAB = 5;

    private Runnable mScrollEndRunable = new Runnable() {
        @Override
        public void run() {
            mContentIsScrollEnd = true;
        }
    };

    private ILayoutListener mLayoutListener = new ILayoutListener() {
        @Override
        public void switchTab(int position) {
            mViewList.get(mCurrentTabIndex).onLayoutHide();
            if (mTempBtn.hasFocus()) {
                mTempBtn.setFocusable(false);
                mTempBtn.setFocusableInTouchMode(false);
                mNavigationView.getFocus();
                return;
            }
            mContentIsScrollEnd = false;
            mCurrentTabIndex = position;
            HomeUtil.setCurrentPageIndex(position);
            ThreadManager.getInstance().removeUiThread(mScrollEndRunable);
            ThreadManager.getInstance().uiThread(mScrollEndRunable, 250);
            mViewList.get(mCurrentTabIndex).onLayoutShow();
            if (mNeedSmoothScroll) {
                mHScrollView.smoothScrollToChild(mViewList.get(position));
            } else {
                mHScrollView.scrollToChild(mViewList.get(position));
            }
            mViewList.get(0).setVisibility(VISIBLE);
            mNeedSmoothScroll = true;
        }

        @Override
        public void loadNextPageData(String url, int id, AppendData appendData, LoadMorePresenter.LoadMoreCallback loadMoreCallback) {
            mHomePresenter.loadNextPageData(url, id, appendData, loadMoreCallback);
        }

        @Override
        public boolean getFocus(int whichView, int from) {
            switch (whichView) {
                case FOCUS_TO_TOP:
                    return mFamilyView.getFocus();
                case FOCUS_TO_TAB:
                    if (mNavigationView.getVisibility() == VISIBLE) {
                        return mNavigationView.getFocus();
                    } else {
                        if (from == FROM_TOP) {
                            return mViewList.get(mCurrentTabIndex).getFocus();
                        } else if (from == FROM_BUTTOM) {
                            return mFamilyView.getFocus();
                        }
                    }
                case FOCUS_TO_CONTENT:
                    if (!mContentIsScrollEnd) {
                        CCLog.i(TAG, "getFocus FOCUS_TO_CONTENT false");
                        return true;
                    }
                    return mViewList.get(mCurrentTabIndex).getFocus();
                default:
                    return false;
            }
        }

        @Override
        public void showTopView() {
            if (mFamilyView.getVisibility() == VISIBLE) {
                return;
            }
            if (mTabIsShow) {
                mNavigationView.setVisibility(VISIBLE);
            }
            mFamilyView.setVisibility(VISIBLE);
            LayoutParams params = (LayoutParams) mHScrollView.getLayoutParams();
            params.topMargin = mContentTopMargin;
            mHScrollView.setLayoutParams(params);
            mHScrollView.requestLayout();
        }

        @Override
        public void hideTopView() {
            if (mFamilyView.getVisibility() == GONE) {
                return;
            }
            if (mTabIsShow) {
                if (mNavigationView.itemHasFocus()) {
                    mTempBtn.setFocusable(true);
                    mTempBtn.setFocusableInTouchMode(true);
                    mTempBtn.bringToFront();
                    mTempBtn.requestFocus();
                }
                mNavigationView.setVisibility(GONE);
            }
            mFamilyView.setVisibility(GONE);
            LayoutParams params = (LayoutParams) mHScrollView.getLayoutParams();
            params.topMargin = 0;
            mHScrollView.setLayoutParams(params);
            mHScrollView.requestLayout();
        }

        @Override
        public void onUserIconFocus(boolean hasFocus) {
            mTempBtn.setFocusable(false);
            mTempBtn.setFocusableInTouchMode(false);
        }
    };

    public HomeView(Context context) {
        super(context);
    }

    @Override
    public void create(Context context, IHomePresenter presenter) {
        setClipChildren(false);
        setClipToPadding(false);
        mHomePresenter = presenter;
        mFamilyView = new FamilyView(context, mLayoutListener);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(76);
        params.topMargin = Util.Div(36);
        params.rightMargin = Util.Div(80);
        addView(mFamilyView, params);

        mNavigationView = new NavigationView(context, mLayoutListener);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(38);
        params.topMargin = Util.Div(136);
        addView(mNavigationView, params);

        mTempBtn = new TextView(context);
        mTempBtn.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                CCLog.i(TAG, "tempBtn onFocusChange:" + b);
            }
        });
        params = new LayoutParams(Util.Div(1), Util.Div(1));
        params.leftMargin = Util.Div(20);
        params.topMargin = Util.Div(20);
        addView(mTempBtn, params);

        initHScrollView();
    }

    @Override
    public void onNewIntent(Intent intent) {
        if (intent == null) {
            return;
        }
        DialogCommonUtil.dismissAlllDialog();
        CCLog.i(TAG, "onNewIntent.");
        //接收从别的模块触发的打开控制面板、添加设备等操作
        String deviceId = intent.getStringExtra(Constants.KEY_DEVICE_ID);
        HomeUtil.setOpenCtrlPanelDeviceId(deviceId);
        if (EmptyUtils.isNotEmpty(HomeUtil.getOpenCtrlPanelDeviceId())) {
            if (Constants.DO_WHAT_ADD_DEVICE.equals(HomeUtil.getOpenCtrlPanelDeviceId())) {
                try {
                    //打开添加设备
                    Intent guide = new Intent("com.smarthome.action.DEVICE_GUIDE");
                    guide.setFlags(FLAG_ACTIVITY_NEW_TASK);
                    getContext().startActivity(guide);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                //打开控制面板
                List<DeviceInfo> datas = ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList();
                if (EmptyUtils.isNotEmpty(datas)) {
                    int size = datas.size();
                    for (int i = 0; i < size; i++) {
                        DeviceInfo info = datas.get(i);
                        if (info.device_id.equals(HomeUtil.getOpenCtrlPanelDeviceId())) {
                            if (info.online_status == 0) {//离线
                                if (info.acess_type == AppConstants.DEVICE_ACESS_TYPE_WIFI) {
                                    //wifi
                                    if (mOfflineHelpWifiDialog == null) {
                                        mOfflineHelpWifiDialog = new OfflineHelpWifiDialog(getContext());
                                    }
                                    mOfflineHelpWifiDialog.show();
                                    DialogCommonUtil.putDialog(DialogCommonUtil.DIALOG_KEY_OFFLINE_HELP_WIFI,mOfflineHelpWifiDialog);
                                    return;
                                }
                                if (info.acess_type == AppConstants.DEVICE_ACESS_TYPE_ZIGBEE || info.acess_type == AppConstants.DEVICE_ACESS_TYPE_BLE) {
                                    //zigbee 、ble
                                    if (mOfflineHelpZigbeeDialog == null) {
                                        mOfflineHelpZigbeeDialog = new OfflineHelpZigbeeDialog(getContext());
                                    }
                                    mOfflineHelpZigbeeDialog.show();
                                    return;
                                }
                            }
                            Map<String, String> map = new HashMap<>();
                            map.put("device_id", info.device_id);
                            if(mDeviceControlDialog == null){
                                mDeviceControlDialog =  new DeviceControlDialog(getContext());
                            }
                            mDeviceControlDialog.showDialog(map);
                            break;
                        }
                    }
                }
            }
            //清空
            HomeUtil.setOpenCtrlPanelDeviceId("");
        }
    }

    @Override
    public View getView() {
        return this;
    }

    private void initHScrollView() {
        mHScrollView = new HorizontalScrollView(getContext());
        mHScrollView.setClipChildren(false);
        mHScrollView.setClipToPadding(false);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        params.topMargin = mContentTopMargin;
        addView(mHScrollView, params);

        mHScrollContentLayout = new LinearLayout(getContext());
        mHScrollContentLayout.setClipChildren(false);
        mHScrollContentLayout.setClipToPadding(false);
        mHScrollView.addView(mHScrollContentLayout, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        mHScrollView.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                return true;
            }
        });
    }

    @Override
    public void refreshTabUI(final List<NavigationData> datas) {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                //只有一个智能设备tab
                int tabSize = datas.size();
                if (tabSize == 1) {
                    mTabIsShow = false;
                    mNavigationView.setVisibility(GONE);
                    mContentTopMargin = mContentTopMargin - Util.Div(110);
                    LayoutParams params = (LayoutParams) mHScrollView.getLayoutParams();
                    params.topMargin = mContentTopMargin;
                    mHScrollView.setLayoutParams(params);
                    mHScrollView.requestLayout();
                } else {
                    mTabIsShow = true;
                    mContentTopMargin = Util.Div(225);
                    mNavigationView.setVisibility(VISIBLE);
                    int index = 0;
                    if (EmptyUtils.isNotEmpty(HomeUtil.getOpenCtrlPanelDeviceId())) {
                        CCLog.i(TAG, "SmartDeviceTabIndex:" + HomeUtil.getSmartDeviceTabIndex() + " -- OpenId:" + HomeUtil.getOpenCtrlPanelDeviceId());
                        index = HomeUtil.getSmartDeviceTabIndex();
                        mNeedSmoothScroll = false;
                    }
                    mNavigationView.refreshUI(datas, index);
                }
                PluginManager.getInstance().destroy();
                mViewList.clear();
                mHScrollContentLayout.removeAllViews();
                for (int i = 0; i < tabSize; i++) {
                    //提前创建好contentView
                    createContentView();
                }
                if (HomeUtil.getSmartDeviceTabIndex() > 0 && !mNeedSmoothScroll) {
                    //避免出现从信号源跳转到设备tab的时候，会先闪一下第一个版面的问题，先隐藏掉
                    mViewList.get(0).setVisibility(INVISIBLE);
                }
                if (!mLayoutIsReady) {
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mLayoutIsReady = true;
                        }
                    }, 1200);
                }
                return Unit.INSTANCE;
            }
        });
    }

    private void createContentView() {
        ContentView contentView = new ContentView(getContext(), mLayoutListener);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(Util.Div(1920), ViewGroup.LayoutParams.MATCH_PARENT);
        mHScrollContentLayout.addView(contentView, params);
        mViewList.add(contentView);
    }

    @Override
    public void createContentView(Container container, int tabIndex) {
        if (mViewList.size() > 0 && tabIndex < mViewList.size()) {
            mViewList.get(tabIndex).create(container);
        }
    }

    @Override
    public FamilyView getFamilyView() {
        return mFamilyView;
    }

    @Override
    public boolean onKeyDown(int keyCode) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            try {
                return ((ContentView) mHScrollContentLayout.getChildAt(mCurrentTabIndex)).onKeyBack();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            switch (keyCode) {
                case KeyEvent.KEYCODE_DPAD_UP:
                case KeyEvent.KEYCODE_DPAD_DOWN:
                case KeyEvent.KEYCODE_DPAD_LEFT:
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    if (!mLayoutIsReady) {
                        return true;
                    }
                    break;
                default:
                    break;
            }
        }
        return false;
    }

    @Override
    public void onResume() {
        for (ContentView contentView : mViewList) {
            contentView.onResume();
        }
    }

    @Override
    public void onPause() {
        for (ContentView contentView : mViewList) {
            contentView.onPause();
        }
    }

    @Override
    public void onStop() {
        for (ContentView contentView : mViewList) {
            contentView.onStop();
        }
    }

    @Override
    public void onDestroy() {
        mCurrentTabIndex = 0;
        DialogCommonUtil.dismissAlllDialog();
        ThreadManager.getInstance().removeUiThread(mScrollEndRunable);
        if (mFamilyView != null) {
            mFamilyView.destroy();
        }
        if (mNavigationView != null) {
            mNavigationView.destroy();
        }
        for (ContentView contentView : mViewList) {
            contentView.onDestroy();
        }
    }
}
