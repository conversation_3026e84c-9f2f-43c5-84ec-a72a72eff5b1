package com.skyworth.smarthome_tv.home.custom.block;

import android.content.Context;

import com.coocaa.operate6_0.model.Container;
import com.skyworth.smarthome_tv.home.custom.BasePluginLayout;
import com.skyworth.smarthome_tv.home.custom.BasePluginPresenter;
import com.skyworth.smarthome_tv.home.custom.Custom;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/29
 */
public class PluginBlockPresenter extends BasePluginPresenter {

    public PluginBlockPresenter(Context context, String type) {
        super(context, type);
    }

    @Override
    protected void init() {
        PKGS.put(Custom.TYPE_MESSAGE, Custom.PKG_MESSAGE);
        PKGS.put(Custom.TYPE_IOT_CHANNEL, Custom.PKG_IOT_CHANNEL);
    }

    @Override
    protected BasePluginLayout getPluginLayout() {
        return new PluginBlockLayout(mContext);
    }

    @Override
    public void setContainer(Container container) {
        container.focusable = 0;//插件类型的block重置focusable为0
        super.setContainer(container);
    }
}
