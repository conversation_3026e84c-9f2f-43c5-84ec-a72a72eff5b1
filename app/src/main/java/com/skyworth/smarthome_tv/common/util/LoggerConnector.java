package com.skyworth.smarthome_tv.common.util;

import android.content.Context;

import com.skyworth.smarthome.BuildConfig;
import com.skyworth.smarthome.account.AppAccountManager;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomeConnector;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.sal.SalImpl;

import java.util.Map;

/**
 * @Description: 参考新TV主页插件的接口，给插件实现部分可用接口：user、logger
 * @Author: wzh
 * @CreateDate: 2020/8/11
 */
public class LoggerConnector implements ISmartHomeConnector {
    private final Context mContext;
    private User mUser;
    private Logger mLogger;
    private Iot mIot;

    public LoggerConnector(Context context) {
        mContext = context;
    }

    @Override
    public User user() {
        //先写着，不一定有插件用到
        if (mUser == null) {
            mUser = new User() {
                @Override
                public UserInfo getUserInfo() {
                    UserInfo userInfo = new UserInfo();
                    try {
                        userInfo.info = SalImpl.getSAL(mContext).getAccoutInfo();
                        userInfo.token = com.skyworth.smarthome.common.model.UserInfo.getInstance().getToken();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return userInfo;
                }

                @Override
                public boolean hasLogin() {
                    return AppAccountManager.INSTANCE.hasLogin();
                }

                @Override
                public boolean login(Map<String, String> params, boolean needFinish, String from) {
                    AppAccountManager.INSTANCE.gotoLogin();
                    return true;
                }

                @Override
                public void addUserChangeListener(UserChangeListener listener) {

                }

                @Override
                public void removeUserChangeListener(UserChangeListener listener) {

                }
            };
        }
        return mUser;
    }

    @Override
    public Executor executor() {
        return null;
    }

    @Override
    public Logger logger() {
        if (mLogger == null) {
            mLogger = new Logger() {
                @Override
                public void pageResumeEvent(String pageName, Map<String, String> params) {

                }

                @Override
                public void pagePausedEvent(String pageName, Map<String, String> params) {

                }

                @Override
                public void pageFailEvent(String pageName, String result, int errorCode) {

                }

                @Override
                public void pageCustomEvent(String eventId, Map<String, String> params) {

                }

                @Override
                public void baseEvent(String eventId, Map<String, String> params) {
                    LogSDK.submit(eventId, params);
                }

                @Override
                public void baseEventSync(String eventId, Map<String, String> params) {
                    LogSDK.submitSync(eventId, params);
                }

                @Override
                public void submitBaseEventWithPolicy(String eventId, Map<String, String> params, int policyTime, int policyMaxLine) {

                }
            };
        }
        return mLogger;
    }

    @Override
    public boolean debugMode() {
        return BuildConfig.DEBUG;
    }

    @Override
    public int homeVersion() {
        return BuildConfig.VERSION_CODE;
    }

    @Override
    public Iot iot() {
        if (mIot == null) {
            mIot = new Iot() {
                @Override
                public String accessToken() {
                    return null;
                }

                @Override
                public void addListener(IotListener listener) {

                }

                @Override
                public void removeListener(IotListener listener) {

                }
            };
        }
        return mIot;
    }
}
