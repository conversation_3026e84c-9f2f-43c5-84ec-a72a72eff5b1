package com.skyworth.smarthome_tv.common.bean;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public class HomeTabBean implements Serializable {
    public int homepage_id;//首页自增ID
    public String homepage_version;//首页的系统版本号
    public String md5Str;
    public String policyCode;//策略响应code    10000:正常获取策略，10404:获取不到策略
    public int policy_id;//策略自增ID
    public String distribute_model;//派发模式，默认“default”
    public List<TabData> array;//关联的版面信息

    public static class TabData {
        public int tag_id;//版面自增ID
        public int tag_index;//版面排序
        public String tag_name_cn;//版面的中文标题
        public int is_record;//1:记录二级版面落焦记录，0:不记录
        public int is_focus;//	1-落焦，0-不落焦
        public int is_fix;//tab是否固定位置，0-不固定，1-固定
        public int is_init_in_category;//tab是否初始化首页分类中，0-否，1-是
        public String type;//无:普通版面，REFERENCE_CHANNEL_MGR：标记版面-频道管理
        public String icon_id;//图标ID
        public String corner_icon_id;//角标ID
        public DmpInfo dmp_info;//版面关联的dmp信息
        public int tag_category;//tab 所属分类 体育66 购物61 教育60 少儿65 应用32 影视31 不限67 生活62
        public List<SubTag> sub_tags;//关联的子版面信息
        public String tab_title_icons;//版面图片标题（有3张，默认选中-selected_img_url,聚焦-focus_img_url,非聚焦-unfocus_img_url）
        //        public List<> system_plugin_id_list;//关联的系统功能id信息（目前只显示有活动浮窗id）
        public TabPluginInfo tab_plugin_info;//版面插件信息
    }

    public static class DmpInfo implements Serializable {
        public int policy_id;//策略id
        public int crowd_id;//人群id
        public int resource_id;//版面id
    }

    public static class SubTag implements Serializable {
        public int tag_id;//版面自增ID
        public int tag_index;//版面排序
        public String tag_name_cn;//版面的中文标题
        public String tag_name_en;//版面的英文标题
        public int tag_category;//tab 所属分类 67:无限 66:体育 65:品牌 64:旅游 63:音乐 62:生活 61:购物 60:教育 33:游戏 32:应用 31:影视
        public String tag_content_md5;//tab内容的MD5值
        public int cycle_time;//tab刷新频率，单位:min
        public String icon_id;//图标ID
        public String corner_icon_id;//角标ID
        public int is_focus;//1-落焦，0-不落焦
        public String tab_title_icons;//版面图片标题（有3张，默认选中-selected_img_url,聚焦-focus_img_url,非聚焦-unfocus_img_url）
    }

    public static class TabPluginInfo implements Serializable {
        public String packagename;//插件包名
        public String category;//插件分类
        public int min_homepage_version;//插件主页最低版本
        public String params;//扩展参数
    }
}
