package com.skyworth.smarthome_tv.common.http;

import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.smarthome.common.sal.SalImpl;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON> on 2018/4/17.
 */
public class HomePageHttpConfig {
    public static final String HOMEPAGE_SERVER = "HOMEPAGE_SERVER";

    public static String getServer(String metaDataKey) {
        return FuncKt.getMetaData(SmartHomeTvLib.getContext(), SmartHomeTvLib.getContext().getPackageName(), metaDataKey);
    }

    public static final HttpServiceManager.HeaderLoader SMARTHOME_HEADER_LOADER = new MyHeaderLoader();

    public static class MyHeaderLoader implements HttpServiceManager.HeaderLoader {

        Map<String, String> DEFAULT_HEADERS = null;

        private Map<String, String> loadHeader() {
            DEFAULT_HEADERS = new HashMap<>();
            Map<String, String> headerMap = SalImpl.getSAL(SmartHomeTvLib.getContext()).getCommonHeader();
            if (headerMap != null && headerMap.size() > 0) {
                DEFAULT_HEADERS.putAll(headerMap);
            }
            return DEFAULT_HEADERS;
        }

        @Override
        public synchronized Map<String, String> getHeader() {
            if (DEFAULT_HEADERS == null || DEFAULT_HEADERS.size() < 1) return loadHeader();
            return DEFAULT_HEADERS;
        }

        @Override
        public synchronized void updateHeader() {
            DEFAULT_HEADERS = null;
            getHeader();
        }
    }

}
