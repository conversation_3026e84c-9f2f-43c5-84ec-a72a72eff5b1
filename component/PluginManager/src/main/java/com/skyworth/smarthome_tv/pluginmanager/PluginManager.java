package com.skyworth.smarthome_tv.pluginmanager;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.WindowManager;

import com.ccos.plugin.Plugin;
import com.ccos.plugin.loader.BaseContext;
import com.ccos.plugin.loader.PluginLoader;
import com.ccos.tvlauncher.sdk.IPluginConnector;
import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.ViewBoundaryProxy;
import com.skyworth.smarthome_tv.pluginmanager.conector.PluginConnectorProxy;
import com.skyworth.smarthome_tv.pluginmanager.lifecyclecallback.LifeCycleProxy;
import com.skyworth.smarthome_tv.pluginmanager.type.IPluginViewType;
import com.skyworth.smarthome_tv.pluginmanager.type.LoadedView;
import com.skyworth.smarthome_tv.pluginmanager.type.PluginTypeFactory;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomeConnector;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomePluginInterface;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ShortcutState;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome_tv.pluginmanager.PluginInfo.PLUGIN_STATE_LOADING;
import static com.skyworth.smarthome_tv.pluginmanager.PluginInfo.PLUGIN_STATE_NOT_LOAD;
import static com.skyworth.smarthome_tv.pluginmanager.PluginInfo.PLUGIN_STATE_READY;
import static com.skyworth.smarthome_tv.pluginmanager.PluginInterfaceUtils.getPluginInterface;
import static com.skyworth.smarthome_tv.pluginmanager.PluginInterfaceUtils.queryInterfaceName;

/**
 * @ClassName: PluginManager
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/10 17:24
 * @Description: 通过插件SDK加载插件，管理加载过程，从插件中获取view
 */
public class PluginManager implements IPluginManager {
    public static final String TAG = "SmartHomePluginManager";
    private final Map<String, PluginInfo> mLoadedPlugin = new HashMap<>();
    private final List<PluginRequestData> mWaitingList = new ArrayList<>();

    private PluginLoader mPluginLoader = null;
    private ISmartHomeConnector pluginConnectorProxy = null;

    private PluginManager() {

    }

    public static IPluginManager getInstance() {
        return PluginManagerClass.instance;
    }

    private static class PluginManagerClass {
        private static IPluginManager instance = new PluginManager();
    }

    @Override
    public void init(Context context, WindowManager windowManager) {
        PluginLoader.init(new BaseContext.Builder(context).setWindowManager(windowManager).build());
        mPluginLoader = PluginLoader.getManager();
    }

    @Override
    public void getView(final String packageName, final String type, final IPluginViewLoadListener listener, IBoundaryCallback callback) {
        IPluginViewType viewType = PluginTypeFactory.create(type);
        final PluginRequestData requestData = new PluginRequestData(packageName, viewType, listener, new ViewBoundaryProxy(callback));
        if (isPluginReady(packageName)) {
            Log.i(TAG, "getView: apk: " + packageName + " ready");
            returnViewInMainThread(requestData);
            return;
        }
        synchronized (mLoadedPlugin) {
            if (isPluginReady(packageName)) {
                Log.i(TAG, "getView: apk: " + packageName + " ready");
                returnViewInMainThread(requestData);
                return;
            }
            FuncKt.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    if (isPluginNotLoad(packageName)) {
                        loadPlugin(packageName);
                    }
                    returnViewLater(requestData);
                    return Unit.INSTANCE;
                }
            });
        }
    }

    @Override
    public void onDeliverPluginMessage(final Bundle bundle) {
        FuncKt.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                synchronized (mLoadedPlugin) {
                    for (PluginInfo pluginInfo : mLoadedPlugin.values()) {
                        try {
                            pluginInfo.i.onDeliverPluginMessage(bundle);
                        } catch (Throwable e) {
                            e.printStackTrace();
                        }
                    }
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void onShortcutStateChanged(@ShortcutState final int state) {
        FuncKt.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                synchronized (mLoadedPlugin) {
                    for (PluginInfo pluginInfo : mLoadedPlugin.values()) {
                        try {
                            pluginInfo.i.onShortcutStateChanged(state);
                        } catch (Throwable e) {
                            e.printStackTrace();
                        }
                    }
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void setConnector(IPluginConnector connector) {
        if (pluginConnectorProxy == null) {
            pluginConnectorProxy = new PluginConnectorProxy(connector);
        }
    }

    @Override
    public void setConnector2(ISmartHomeConnector connector) {
        pluginConnectorProxy = connector;
    }

    @Override
    public void destroy() {
        Log.w(TAG, "destroy!!!");
        synchronized (mLoadedPlugin) {
            for (PluginInfo value : mLoadedPlugin.values()) {
                if (value != null && mPluginLoader != null) {
                    Plugin plugin = value.plugin;
                    if (plugin == null) {
                        continue;
                    }
                    Log.i(TAG, "destroy: release: " + plugin.packageName);
//                    mPluginLoader.release(plugin);
                    value.plugin = null;
                }
            }
            mLoadedPlugin.clear();
        }
        synchronized (mWaitingList) {
            mWaitingList.clear();
        }
    }

    private void returnViewInMainThread(final PluginRequestData pluginRequestData) {
        if (!PluginRequestData.checkData(pluginRequestData)) {
            return;
        }
        Log.i(TAG, "returnViewInMainThread: " + pluginRequestData.packageName);
        FuncKt.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                LoadedView loadedView = loadViewFromPlugin(pluginRequestData);
                if (loadedView != null) {
                    pluginRequestData.loadListener.onLoadSuccess(pluginRequestData.packageName, pluginRequestData.type, loadedView.view, new LifeCycleProxy(loadedView.lifeCycleCallback));
                } else {
                    pluginRequestData.loadListener.onLoadFail(pluginRequestData.packageName, pluginRequestData.type, IPluginViewLoadListener.FAIL_REASON_LOAD_INTERFACE_ERROR);
                }
                return Unit.INSTANCE;
            }
        });
    }

    private void returnViewLater(PluginRequestData pluginRequestData) {
        if (!PluginRequestData.checkData(pluginRequestData)) {
            return;
        }
        Log.i(TAG, "returnViewLater: " + pluginRequestData.packageName);
        synchronized (mWaitingList) {
            mWaitingList.add(pluginRequestData);
        }
    }

    private boolean isPluginNotLoad(String packageName) {
        PluginInfo info = mLoadedPlugin.get(packageName);
        if (info == null) {
            return true;
        }
        return info.state == PluginInfo.PLUGIN_STATE_NOT_LOAD;
    }

    private LoadedView loadViewFromPlugin(PluginRequestData requestData) {
        PluginInfo pluginInfo = mLoadedPlugin.get(requestData.packageName);
        if (pluginInfo == null) {
            return null;
        }
        return requestData.type.loadView(pluginInfo.i, requestData);
    }

    private boolean isPluginReady(String packageName) {
        PluginInfo info = mLoadedPlugin.get(packageName);
        if (info == null) {
            return false;
        }
        return info.state == PluginInfo.PLUGIN_STATE_READY;
    }

    private void loadPlugin(final String packageName) {
        Log.i(TAG, "loadPlugin: " + packageName);
        mLoadedPlugin.put(packageName, new PluginInfo(null, null, PLUGIN_STATE_LOADING));
        Uri uri = PluginLoader.fromPackage(packageName);
        mPluginLoader.load(uri, new PluginLoader.LoaderCallback() {
            @Override
            public void onPluginLoadSuccess(Uri uri, Plugin plugin) {
                Log.i(TAG, "onPluginLoadSuccess: " + packageName);
                handlePluginLoadSuccess(plugin, packageName);
            }

            @Override
            public void onPluginLoadFailed(Uri uri, Throwable throwable) {
                Log.i(TAG, "onPluginLoadFailed: " + packageName);
                throwable.printStackTrace();
                handlePluginLoadFailed(packageName, IPluginViewLoadListener.FAIL_REASON_LOAD_APK_ERROR);
            }
        });
    }

    private void handlePluginLoadSuccess(Plugin plugin, String packageName) {
        ISmartHomePluginInterface i;
        synchronized (mLoadedPlugin) {
            String interfaceName = queryInterfaceName(plugin.context, packageName);
            i = getPluginInterface(plugin.classLoader, interfaceName);
            PluginInfo info = mLoadedPlugin.get(packageName);
            if (i != null && info != null) {
                info.state = PLUGIN_STATE_READY;
                info.i = i;
                info.plugin = plugin;
            } else if (info != null) {
                Log.e(TAG, "onPluginLoadSuccess: get interface null !!!");
                info.state = PLUGIN_STATE_NOT_LOAD;
                info.i = i;
                info.plugin = plugin;
            } else {
                Log.e(TAG, "onPluginLoadSuccess: get interface null !!! (info == null)");
            }
        }
        if (i != null) {
            try {
                i.onContextSet(plugin.context);
            } catch (Throwable t) {
                t.printStackTrace();
            }
            try {
                i.setSmartHomeConnector(pluginConnectorProxy);
            } catch (Throwable t) {
                t.printStackTrace();
            }
            try {
                i.onPluginInit();
            } catch (Throwable t) {
                t.printStackTrace();
            }
        }
        executeWaitList(packageName);
    }

    private void executeWaitList(String packageName) {
        synchronized (mWaitingList) {
            Iterator<PluginRequestData> iterator = mWaitingList.iterator();
            while (iterator.hasNext()) {
                PluginRequestData requestData = iterator.next();
                if (packageName.equals(requestData.packageName)) {
                    Log.i(TAG, "exeCuteWaitList: " + packageName);
                    returnViewInMainThread(requestData);
                    iterator.remove();
                }
            }
        }
    }

    private void handlePluginLoadFailed(final String packageName, final String failReason) {
        synchronized (mLoadedPlugin) {
            mLoadedPlugin.remove(packageName);
        }
        synchronized (mWaitingList) {
            Iterator<PluginRequestData> iterator = mWaitingList.iterator();
            while (iterator.hasNext()) {
                final PluginRequestData requestData = iterator.next();
                if (packageName.equals(requestData.packageName)) {
                    Log.i(TAG, "handlePluginLoadFailed: remove waiting list " + packageName);
                    FuncKt.uiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            try {
                                Log.i(TAG, "handlePluginLoadFailed onLoadFail callback: " + packageName);
                                requestData.loadListener.onLoadFail(packageName, requestData.type, failReason);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            return Unit.INSTANCE;
                        }
                    });
                    iterator.remove();
                }
            }
        }
    }
}
