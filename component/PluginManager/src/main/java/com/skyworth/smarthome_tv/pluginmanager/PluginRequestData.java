package com.skyworth.smarthome_tv.pluginmanager;

import com.skyworth.smarthome_tv.pluginmanager.type.IPluginViewType;
import com.skyworth.smarthome_tv.smarthomeplugininterface.IViewBoundaryCallback;

/**
 * @ClassName: WaitPluginRequest
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/15 17:08
 * @Description:
 */
public class PluginRequestData {
    public String packageName;
    public IPluginViewType type;
    public IPluginViewLoadListener loadListener;
    public IViewBoundaryCallback boundaryCallback;

    public PluginRequestData(String packageName, IPluginViewType type, IPluginViewLoadListener loadListener, IViewBoundaryCallback boundaryCallback) {
        this.packageName = packageName;
        this.type = type;
        this.loadListener = loadListener;
        this.boundaryCallback = boundaryCallback;
    }

    public static boolean checkData(PluginRequestData data) {
        if (data == null) {
            return false;
        }
        String packageName = data.packageName;
        IPluginViewType type = data.type;
        IPluginViewLoadListener loadListener = data.loadListener;
        IViewBoundaryCallback callback = data.boundaryCallback;
        if (packageName == null || type == null || loadListener == null || callback == null) {
            return false;
        }
        return true;
    }
}
