package com.skyworth.smarthome_tv.pluginmanager.type;

import android.view.View;

import com.skyworth.smarthome_tv.pluginmanager.PluginRequestData;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomePluginInterface;

/**
 * @ClassName: PluginTypeCardView
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/15 17:41
 * @Description:
 */
public class PluginViewTypeCardView implements IPluginViewType {
    public static final String TYPE = "CARD_VIEW";
    private static final PluginViewTypeCardView INSTANCE = new PluginViewTypeCardView();

    private PluginViewTypeCardView() {

    }

    public static PluginViewTypeCardView get() {
        return INSTANCE;
    }

    @Override
    public LoadedView loadView(ISmartHomePluginInterface iSmartHomePluginInterface, PluginRequestData requestData) {
        if (iSmartHomePluginInterface == null || requestData == null) {
            return null;
        }
        LoadedView loadedView = new LoadedView();
        try {
            loadedView.view = iSmartHomePluginInterface.getContentCardView(requestData.boundaryCallback);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        try {
            loadedView.lifeCycleCallback = iSmartHomePluginInterface.getContentLifeCycleCallback();
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return loadedView;
    }
}
