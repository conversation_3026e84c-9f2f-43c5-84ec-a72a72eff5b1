package com.skyworth.smarthome_tv.pluginmanager;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;

import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomePluginInterface;

import static com.skyworth.smarthome_tv.pluginmanager.PluginManager.TAG;

import static android.content.pm.PackageManager.GET_META_DATA;

/**
 * @ClassName: PluginInterfaceUtils
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/17 15:08
 * @Description:
 */
public class PluginInterfaceUtils {
    private static final String META_DATA_KEY = "SMART_HOME_PLUGIN";

    public static String queryInterfaceName(Context context, String packageName) {
        if (context == null || TextUtils.isEmpty(packageName)) {
            return null;
        }
        PackageManager packageManager = context.getPackageManager();
        ApplicationInfo applicationInfo = null;
        try {
            applicationInfo = packageManager.getApplicationInfo(packageName, GET_META_DATA);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        if (applicationInfo != null && applicationInfo.metaData != null) {
            String interfaceName = applicationInfo.metaData.getString(META_DATA_KEY);
            Log.i(TAG, "queryInterface: " + packageName + " : " + applicationInfo.metaData.getString(META_DATA_KEY));
            return interfaceName;
        }
        return null;
    }

    public static ISmartHomePluginInterface getPluginInterface(ClassLoader classLoader, String interfaceName) {
        if (classLoader == null || TextUtils.isEmpty(interfaceName)) {
            return null;
        }
        Class<?> clazz = null;
        ISmartHomePluginInterface i = null;
        try {
            clazz = classLoader.loadClass(interfaceName);
            i = (ISmartHomePluginInterface) clazz.newInstance();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return i;
    }
}
