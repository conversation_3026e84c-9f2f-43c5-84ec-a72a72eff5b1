package com.skyworth.smarthome_tv.pluginmanager.boundarycallback;

import android.view.View;

import com.skyworth.smarthome_tv.smarthomeplugininterface.IViewBoundaryCallback;

/**
 * @ClassName: ViewBoundaryAdapter
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/22 14:22
 * @Description:
 */
public class ViewBoundaryProxy implements IViewBoundaryCallback {
    private IBoundaryCallback callback = null;

    public ViewBoundaryProxy(IBoundaryCallback callback) {
        if (callback == null) {
            throw new NullPointerException("callback == null");
        }
        this.callback = callback;
    }

    @Override
    public boolean onTopBoundary(View leaveView) {
        return callback.onTop(leaveView);
    }

    @Override
    public boolean onDownBoundary(View leaveView) {
        return callback.onDown(leaveView);
    }

    @Override
    public boolean onLeftBoundary(View leaveView) {
        return callback.onLeft(leaveView);
    }

    @Override
    public boolean onRightBoundary(View leaveView) {
        return callback.onRight(leaveView);
    }

    @Override
    public boolean onBackKey(View leaveView) {
        return callback.onBackKey(leaveView);
    }
}
