package com.skyworth.smarthome_tv.pluginmanager;

import android.view.View;

import com.skyworth.smarthome_tv.pluginmanager.lifecyclecallback.ILifeCycleCallback;
import com.skyworth.smarthome_tv.pluginmanager.type.IPluginViewType;

/**
 * @ClassName: IPluginViewLoadListener
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/15 14:09
 * @Description:
 */
public interface IPluginViewLoadListener {
    public static final String FAIL_REASON_LOAD_APK_ERROR = "FAIL_REASON_LOAD_APK_ERROR";
    public static final String FAIL_REASON_LOAD_INTERFACE_ERROR = "FAIL_REASON_LOAD_INTERFACE_ERROR";

    /**
     * 插件中view加载成功
     *
     * @param packageName
     * @param type
     * @param view
     */
    void onLoadSuccess(String packageName, IPluginViewType type, View view, ILifeCycleCallback cycleCallback);

    /**
     * 插件或插件中view加载失败
     *
     * @param packageName
     * @param type
     */
    void onLoadFail(String packageName, IPluginViewType type, String failReason);
}
