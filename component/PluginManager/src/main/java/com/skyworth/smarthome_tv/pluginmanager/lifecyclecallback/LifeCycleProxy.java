package com.skyworth.smarthome_tv.pluginmanager.lifecyclecallback;

import com.skyworth.smarthome_tv.smarthomeplugininterface.LifeCycleCallback;

/**
 * @ClassName: LifeCycleAdapter
 * @Author: XuZeXiao
 * @CreateDate: 2020/7/8 11:38
 * @Description:
 */
public class LifeCycleProxy implements ILifeCycleCallback {
    private LifeCycleCallback cycleCallback;

    public LifeCycleProxy(LifeCycleCallback cycleCallback) {
        this.cycleCallback = cycleCallback;
    }

    @Override
    public void onResume() {
        if (cycleCallback != null) {
            cycleCallback.onResume();
        }
    }

    @Override
    public void onPause() {
        if (cycleCallback != null) {
            cycleCallback.onPause();
        }
    }

    @Override
    public void onStop() {
        if (cycleCallback != null) {
            cycleCallback.onStop();
        }
    }

    @Override
    public void onDestroy() {
        if (cycleCallback != null) {
            cycleCallback.onDestroy();
        }
    }
}
