package com.skyworth.smarthome_tv.pluginmanager.type;

import android.text.TextUtils;
import android.util.Log;

import static com.skyworth.smarthome_tv.pluginmanager.PluginManager.TAG;

/**
 * @ClassName: PluginTypeFactory
 * @Author: Xu<PERSON>eXiao
 * @CreateDate: 2020/6/15 17:42
 * @Description:
 */
public class PluginTypeFactory {
    public static IPluginViewType create(String type) {
        if (TextUtils.isEmpty(type)) {
            Log.e(TAG, "PluginTypeFactory create type empty");
            return null;
        }
        switch (type) {
            case PluginViewTypeCardView.TYPE:
                return PluginViewTypeCardView.get();
            case PluginViewTypePanelView.TYPE:
                return PluginViewTypePanelView.get();
            default:
                Log.e(TAG, "PluginTypeFactory create type empty");
                return null;
        }
    }
}
