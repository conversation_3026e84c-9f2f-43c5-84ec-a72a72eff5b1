package com.smarthome.plugin.page.main.custom;

import android.content.Context;
import android.support.annotation.NonNull;
import android.view.View;
import android.widget.FrameLayout;

import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.listener.IView;
import com.coocaa.uisdk.model.ContainerV8;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/24
 */
public abstract class BaseBlockLayout  extends FrameLayout implements IView {

    public BaseBlockLayout(@NonNull Context context) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
    }

    @Override
    public void setFocus(boolean focus) {

    }

    @Override
    public void onClick() {

    }

    @Override
    public View makeFocusView() {
        return null;
    }

    @Override
    public View makeContentView() {
        return null;
    }

    @Override
    public int getBorderWidth() {
        return -1;
    }

    @Override
    public int getBorderRadius() {
        return -1;
    }

    @Override
    public boolean isSquare() {
        return false;
    }

    @Override
    public void setSize(int w, int h) {

    }

    @Override
    public void refreshUI() {

    }

    @Override
    public IBlockFocusLightView makeBlockFocusLightView() {
        return null;
    }

    @Override
    public String getPosterUrl() {
        return null;
    }

    @Override
    public void setBlockData(ContainerV8 o) {

    }

    @Override
    public boolean obtainFocus() {
        return false;
    }

    public View getFocusView() {
        return null;
    }

    public void onDestroy() {

    }
}