package com.smarthome.plugin.page.main.custom;

import android.content.Context;
import android.view.View;

import com.coocaa.uisdk.presenter.PresenterV8;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/24
 */
public abstract class BaseBlockPresenter<T extends BaseBlockLayout> extends PresenterV8 {

    protected BaseBlockLayout mLayout;

    public BaseBlockPresenter(Context context) {
        super(context);
        mLayout = makeView(context);
    }

    @Override
    public T makeView(Context context) {
        return null;
    }

    @Override
    public View getView() {
        return mLayout;
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onDestroy() {

    }
}