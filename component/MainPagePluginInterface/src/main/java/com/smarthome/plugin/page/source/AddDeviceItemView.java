package com.smarthome.plugin.page.source;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.R;
import com.smarthome.plugin.page.base.DeviceItemData;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/17
 */
public class AddDeviceItemView extends FrameLayout implements NewRecycleAdapterItem<DeviceItemData> {

    //    public View mBgView;
//    private View mFocusView;
    private ImageView icon;
    private TextView txt;
    private View mLine;
    private CCFocusDrawable mFocusBg;
//    private final static int FOCUS_W = Util.Div(16);

    public AddDeviceItemView(Context context) {
        super(context);
        setFocusable(true);

        mFocusBg = new CCFocusDrawable(context).setRadius(Util.Div(8)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
        setBackground(mFocusBg);

//        mFocusView = new View(getContext());
//        mFocusView.setBackground(XThemeUtils.getDrawable(0, Color.WHITE, Util.Div(4), Util.Div(16)));
//        LayoutParams params = new LayoutParams(Util.Div(560) + FOCUS_W, Util.Div(160) + FOCUS_W);
//        params.gravity = Gravity.CENTER;
//        addView(mFocusView, params);
//        mFocusView.setVisibility(INVISIBLE);
//
//        mBgView = new View(getContext());
//        params = new LayoutParams(Util.Div(560), Util.Div(160));
//        params.gravity = Gravity.CENTER;
//        addView(mBgView, params);

        icon = new ImageView(context);
        icon.setBackgroundResource(R.drawable.ic_add_white);
        LayoutParams params = new LayoutParams(Util.Div(36), Util.Div(36));
        params.gravity = Gravity.CENTER_VERTICAL;
        params.leftMargin = Util.Div(168);
        addView(icon, params);

        txt = new TextView(context);
        txt.setTextColor(Color.WHITE);
        txt.setTextSize(Util.Dpi(32));
        txt.getPaint().setFakeBoldText(true);
        txt.setText("添加设备");
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_VERTICAL;
        params.leftMargin = Util.Div(214);
        addView(txt, params);

        mLine = new View(getContext());
        mLine.setBackgroundColor(Color.parseColor("#10ffffff"));
        params = new LayoutParams(Util.Div(560), Util.Div(1));
        params.topMargin = Util.Div(160 + 4);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mLine, params);
    }

    @Override
    public View getView() {
        return this;
    }

    public void onFocusChange(View view, boolean b) {
        if (b) {
            icon.setBackgroundResource(R.drawable.ic_add_black);
            txt.setTextColor(Color.BLACK);
//            mFocusView.setVisibility(VISIBLE);
//            mBgView.setBackground(XThemeUtils.getDrawable(Color.parseColor("#FFFFFF"), 0, 0, Util.Div(8)));
        } else {
            icon.setBackgroundResource(R.drawable.ic_add_white);
            txt.setTextColor(Color.WHITE);
//            mFocusView.setVisibility(INVISIBLE);
//            mBgView.setBackground(null);
        }
        mFocusBg.setBorderVisible(b).setSolidColor(b ? Color.WHITE : Color.TRANSPARENT);
    }

    @Override
    public void onUpdateData(DeviceItemData deviceItemData, int i) {

    }

    @Override
    public void clearItem() {

    }

    @Override
    public void refreshUI() {

    }

    @Override
    public void destroy() {

    }
}
