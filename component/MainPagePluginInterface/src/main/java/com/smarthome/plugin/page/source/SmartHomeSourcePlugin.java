package com.smarthome.plugin.page.source;

import android.os.Bundle;
import android.view.View;

import com.ccos.tvlauncher.sdk.sourcepage.DefaultSourcePlugin;
import com.skyworth.framework.skysdk.ipc.SkyApplication;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.account.PluginAccount;
import com.smarthome.plugin.aiotdata.IAIOTDataModel;
import com.smarthome.plugin.util.PluginLogger;
import com.swaiot.aiotlib.common.util.ThreadManager;

import java.util.Map;

/**
 * @Description: 信号源插件
 * @Author: wzh
 * @CreateDate: 2020/7/7
 */
public class SmartHomeSourcePlugin extends DefaultSourcePlugin {

    private SourcePluginPresenter mPresenter;
    private SourcePluginView mContentView;

    private Runnable mPluginShowRunnable = new Runnable() {
        @Override
        public void run() {
            PluginLogger.submit(LogSDK.EVENT_ID_LIST_SHOW);
        }
    };

    @Override
    public void onInit() {
        CCLog.init("SmartHomeSourcePlugin");
        CCLog.i("onInit: ");
        init();
        super.onInit();
    }

    private void init() {
        try {
            SkyApplication.init(mPluginContext);
            PluginAccount.getInstance().init(mPluginContext, connector);
            IAIOTDataModel.INSTANCE.init(mPluginContext);
            Util.instence(mPluginContext);
            XThemeUtils.init(mPluginContext);
            ImageLoader.getLoader().init(mPluginContext);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String productId() {
        return LogSDK.PRODUCT_ID;
    }

    @Override
    public void setHeader(Map<String, String> header) {
        super.setHeader(header);
        PluginAccount.getInstance().setHeader(header);
    }

    @Override
    public View makeContentView() {
        CCLog.i("SmartHomeSourcePlugin", "makeContentView...");
        if (mContentView == null) {
//            init();
            mContentView = new SourcePluginView(mPluginContext);
            mContentView.setCallback(callback, this, position);
            mContentView.setISourceView(mISourceView);
            mPresenter = new SourcePluginPresenter(mPluginContext, mContentView, mISourceView);
            mPresenter.loadData();
        }
        return mContentView;
    }

    @Override
    public void onDeliverPluginMessage(Bundle bundle) {
        super.onDeliverPluginMessage(bundle);
        CCLog.i("SmartHomeSourcePlugin", "onDeliverPluginMessage");
        if (mPresenter != null) {
            mPresenter.onDeliverPluginMessage(bundle);
        }
    }

    @Override
    public boolean obtainFocus() {
        CCLog.i("SmartHomeSourcePlugin", "obtainFocus");
//        if (mContentView != null) {
//            return mContentView.getFocus();
//        }
        return super.obtainFocus();
    }

    @Override
    public void onShow() {
        super.onShow();
        CCLog.i("SmartHomeSourcePlugin", "onShow");
        ThreadManager.getInstance().uiThread(mPluginShowRunnable, 300);
    }

    @Override
    public void onHide() {
        super.onHide();
        CCLog.i("SmartHomeSourcePlugin", "onHide");
        ThreadManager.getInstance().removeUiThread(mPluginShowRunnable);
    }

    @Override
    public void onResume() {
        super.onResume();
        CCLog.i("SmartHomeSourcePlugin", "onResume");
    }

    @Override
    public void onPause() {
        super.onPause();
        CCLog.i("SmartHomeSourcePlugin", "onPause");
    }

    @Override
    public void onStop() {
        super.onStop();
        CCLog.i("SmartHomeSourcePlugin", "onStop");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CCLog.i("SmartHomeSourcePlugin", "onDestroy");
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }
}
