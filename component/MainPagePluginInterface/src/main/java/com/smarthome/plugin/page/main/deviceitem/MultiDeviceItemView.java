package com.smarthome.plugin.page.main.deviceitem;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ccos.tvlauncher.sdk.IDirection;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.util.PluginUtil;
import com.smarthome.plugin.page.base.DeviceItemData;
import com.smarthome.plugin.page.main.carditem.DeviceCardItemView;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public class MultiDeviceItemView extends BaseDeviceItemView {

    private LinearLayout mContentLayout;
    private TextView mAllBtn;
    private DeviceCardItemView.IDeviceCardLayout mIDeviceCardLayout;
    private List<SmallDeviceItemView> mViews = new ArrayList<>();

    public MultiDeviceItemView(Context context, int pos, DeviceCardItemView.IDeviceCardLayout iDeviceCardLayout) {
        super(context, pos);
        mIDeviceCardLayout = iDeviceCardLayout;
        mContentLayout = new LinearLayout(context);
        mContentLayout.setOrientation(LinearLayout.VERTICAL);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        params.topMargin = Util.Div(88);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mContentLayout, params);
    }

    @Override
    public void refreshUI(List<DeviceItemData> datas) {
        super.refreshUI(datas);
        final int size = datas.size();
        for (int i = 0; i < size; i++) {
            if (i > 2) {
                break;
            }
            final DeviceItemData data = datas.get(i);
            final SmallDeviceItemView itemView = new SmallDeviceItemView(getContext());
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(Util.Div(350), Util.Div(100));
            params.gravity = Gravity.CENTER_HORIZONTAL;
            if (i > 0) {
                params.topMargin = Util.Div(20);
            }
            mContentLayout.addView(itemView, params);
            mViews.add(itemView);
            itemView.refreshUI(data);
            final int finalI = i;
            itemView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    PluginUtil.startSmartHome(getContext(), data);
                }
            });
            itemView.setOnFocusChangeListener(new OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean b) {
                    itemView.onFocusChange(view, b);
                    mIDeviceCardLayout.onFocusChange(b);
                }
            });
            itemView.setOnKeyListener(new OnKeyListener() {
                @Override
                public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                    if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                        switch (keyCode) {
                            case KeyEvent.KEYCODE_DPAD_UP:
                                if (finalI == 0) {
                                    return onBoundary(itemView, IDirection.TOP);
                                }
                                break;
                            case KeyEvent.KEYCODE_DPAD_DOWN:
                                if (size <= 3 && finalI == (size - 1)) {
                                    return onBoundary(itemView, IDirection.DOWN);
                                }
                                break;
                            case KeyEvent.KEYCODE_DPAD_LEFT:
                                return onBoundary(itemView, IDirection.LEFT);
                            case KeyEvent.KEYCODE_DPAD_RIGHT:
                                return onBoundary(itemView, IDirection.RIGHT);
                            case KeyEvent.KEYCODE_BACK:
                                return onBoundary(itemView, IDirection.BACK);
                            default:
                                break;
                        }
                    }
                    return false;
                }
            });
        }
        if (size > 3) {
            createAllBtn(size);
        }
    }

    @Override
    public boolean itemHasFocus() {
        for (SmallDeviceItemView itemView : mViews) {
            if (itemView.hasFocus()) {
                return true;
            }
        }
        if (mAllBtn != null && mAllBtn.getVisibility() == VISIBLE) {
            return mAllBtn.hasFocus();
        }
        return false;
    }

    @Override
    public void notifyDeviceItemChanged(DeviceItemData updateData) {
        super.notifyDeviceItemChanged(updateData);
        int size = mDataList.size();
        for (int i = 0; i < size; i++) {
            DeviceItemData data = mDataList.get(i);
            if (data.device_id.equals(updateData.device_id)) {
                mViews.get(i).refreshUI(data);
                break;
            }
        }
    }

    @Override
    public boolean onBoundary(View leaveView, @IDirection int direction) {
        if (direction == IDirection.RIGHT || direction == IDirection.DOWN || direction == IDirection.BACK) {
            mIDeviceCardLayout.onFocusChange(false);
        }
        return super.onBoundary(leaveView, direction);
    }

    private void createAllBtn(int size) {
        mAllBtn = new TextView(getContext());
        mAllBtn.setFocusable(true);
        mAllBtn.setTextColor(Color.WHITE);
        mAllBtn.setTextSize(Util.Dpi(25));
        mAllBtn.setGravity(Gravity.CENTER);
        mAllBtn.getPaint().setFakeBoldText(true);
        mAllBtn.setBackground(XThemeUtils.getDrawable(Color.parseColor("#10FFFFFF"), 0, 0, Util.Div(10)));
        mAllBtn.setText("全部 ( " + size + " )");
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(Util.Div(350), Util.Div(60));
        params.gravity = Gravity.CENTER;
        params.topMargin = Util.Div(20);
        mContentLayout.addView(mAllBtn, params);
        mAllBtn.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                if (b) {
                    mIDeviceCardLayout.onFocusChange(true);
                    mAllBtn.setBackground(XThemeUtils.getDrawable(Color.parseColor("#FFFFFF"), 0, 0, Util.Div(10)));
                    mAllBtn.setTextColor(Color.BLACK);
                } else {
                    mAllBtn.setBackground(XThemeUtils.getDrawable(Color.parseColor("#10FFFFFF"), 0, 0, Util.Div(10)));
                    mAllBtn.setTextColor(Color.WHITE);
                }
//                Util.focusAnimate(allButtonLayout, b);
            }
        });
        mAllBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                PluginUtil.startSmartHome(getContext(), null);
            }
        });
        mAllBtn.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT || keyCode == KeyEvent.KEYCODE_BACK) {
                        return onKeyDown(mAllBtn, keyCode);
                    }
                }
                return false;
            }
        });
    }

    @Override
    public boolean getFocus() {
        return mContentLayout.getChildAt(0).requestFocus();
    }
}
