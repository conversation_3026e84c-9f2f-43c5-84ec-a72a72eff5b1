package com.smarthome.plugin.page.main;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.utils.FuncKt;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.plugin.account.PluginAccount;
import com.smarthome.plugin.model.OperationCardBean;
import com.smarthome.plugin.model.SmartHomeHttpService;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import swaiotos.sal.network.INetwork;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/19
 */
public class SmartHomePresenter {

    private Context mContext;
    private SmartHomePluginView mView;
    private boolean needReload = false;

    private BroadcastReceiver mNetBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null && action.equals(Constants.BROADCAST_ACTION_NET_CHANGE)) {
                boolean hasNet = Android.isNetConnected(mContext);
                CCLog.i("SmartHomePresenter", "onReceive onNetChanged : " + hasNet + "--needReload:" + needReload);
                if (hasNet && needReload) {
                    loadData();
                }
            }
        }
    };

//    private INetwork.INetworkListener iNetworkListener = new INetwork.INetworkListener() {
//        @Override
//        public void onNetChanged(boolean b, int i) {
//            CCLog.i("SmartHomePresenter", "onNetChanged : " + b + "---needReload:" + needReload);
//            if (b && needReload) {
//                CCLog.i("SmartHomePresenter", "onNetChanged : reload data.");
//                loadData();
//            }
//        }
//    };

    public SmartHomePresenter(Context context, SmartHomePluginView view) {
        mContext = context;
        mView = view;
        IntentFilter filter = new IntentFilter();
        filter.addAction(Constants.BROADCAST_ACTION_NET_CHANGE);
        mContext.registerReceiver(mNetBroadcastReceiver, filter);
//        SalImpl.getSAL(mContext).addNetListener(iNetworkListener);
    }

    public void loadData() {
        if (!Android.isNetConnected(mContext)) {
            needReload = true;
            return;
        }
        needReload = false;
        HttpServiceManager.Companion.call(SmartHomeHttpService.SERVICE.getOperationCardList(Constants.OPERATION_CARD), new Function1<HttpServiceManager.ERROR, Unit>() {
            @Override
            public Unit invoke(HttpServiceManager.ERROR error) {
                CCLog.i("SmartHomePresenter", "getOperationCardList onError: " + error.getMsg());
                createDefaultData();
                return Unit.INSTANCE;
            }
        }, new Function1<SmartBaseData<OperationCardBean>, Unit>() {
            @Override
            public Unit invoke(final SmartBaseData<OperationCardBean> baseData) {
                CCLog.i("SmartHomePresenter", "getOperationCardList: onSuccess:" + baseData.code);
                if (EmptyUtils.isNotEmpty(baseData.data) && EmptyUtils.isNotEmpty(baseData.data.cards)) {
                    FuncKt.runOnUiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
//                            mView.refreshUI(baseData.data.cards);
                            return Unit.INSTANCE;
                        }
                    });
                } else {
                    createDefaultData();
                }
                return Unit.INSTANCE;
            }
        });
    }

    private void createDefaultData() {
        CCLog.i("SmartHomePresenter", "createDefaultData.");
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                List<OperationCardBean.CardData> datas = new ArrayList<>();
//                datas.add(new OperationCardBean.CardData("com.coocaa.smartmall", "购物", "", "CARD_VIEW"));
                datas.add(new OperationCardBean.CardData("com.skyworth.smarthome_tv", "智能设备", "https://tv.doubimeizhi.com/images/tv/cards/smart_device.png", "CARD_VIEW"));
                datas.add(new OperationCardBean.CardData("com.coocaa.tvpitv", "视频通话", "https://tv.doubimeizhi.com/images/tv/cards/video_call.png", "CARD_VIEW"));
                datas.add(new OperationCardBean.CardData("com.trensai.msgboard", "留言板", "https://tv.doubimeizhi.com/images/tv/cards/message_board.png", "CARD_VIEW"));
                datas.add(new OperationCardBean.CardData("swaiotos.channel.iot", "跨屏互动", "https://tv.doubimeizhi.com/images/tv/cards/screen_interact_default.png", "CARD_VIEW"));
//                mView.refreshUI(datas);
                return Unit.INSTANCE;
            }
        });
    }

    private void runIOThread(Runnable runnable) {
        PluginAccount.getInstance().executor().execute(runnable);
    }

    public void onDestroy() {
//        SalImpl.getSAL(mContext).removeNetListener(iNetworkListener);
        mContext.unregisterReceiver(mNetBroadcastReceiver);
    }
}
