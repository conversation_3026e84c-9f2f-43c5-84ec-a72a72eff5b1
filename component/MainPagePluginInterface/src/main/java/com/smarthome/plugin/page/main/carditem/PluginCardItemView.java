package com.smarthome.plugin.page.main.carditem;

import android.content.Context;
import android.content.Intent;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;

import com.ccos.tvlauncher.sdk.IDirection;
import com.skyworth.smarthome_tv.pluginmanager.IPluginViewLoadListener;
import com.skyworth.smarthome_tv.pluginmanager.PluginManager;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;
import com.skyworth.smarthome_tv.pluginmanager.lifecyclecallback.ILifeCycleCallback;
import com.skyworth.smarthome_tv.pluginmanager.type.IPluginViewType;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.plugin.model.OperationCardBean;
import com.smarthome.plugin.util.PluginLogger;
import com.swaiot.aiotlib.common.util.ThreadManager;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public class PluginCardItemView extends BaseCardItemView<OperationCardBean.CardData> {
    private View mDefaultFocusView;
    private ILifeCycleCallback mLifeCycleCallback;
    private OperationCardBean.CardData mCardData;
    private boolean pluginIsLoaded = false;
    private String hasPlugin = "no";
    private String mReason = "";
    private Runnable mPluginShowRunnable = new Runnable() {
        @Override
        public void run() {
            if (pluginIsLoaded) {
                Map<String, String> params = new HashMap<>();
                params.put("plugin_name", mCardData.cardName);
                params.put("if_plugin", hasPlugin);
                params.put("reason", mReason);
                PluginLogger.submit(LogSDK.EVENT_ID_PLUGIN_SHOW, params);
            }
        }
    };

    public PluginCardItemView(Context context, int pos) {
        super(context, pos);
    }

    @Override
    public void refreshUI(final OperationCardBean.CardData data) {
        mCardData = data;
        setFocusable(false);
        PluginManager.getInstance().getView(data.pkg, data.viewType, new IPluginViewLoadListener() {
            @Override
            public void onLoadSuccess(String packageName, IPluginViewType type, View view, ILifeCycleCallback callback) {
                CCLog.i("PluginCardItemView", "load plugin success:" + packageName);
                mLifeCycleCallback = callback;
                pluginIsLoaded = true;
                hasPlugin = "yes";
                setContentView(view);
                ThreadManager.getInstance().uiThread(mPluginShowRunnable, 300);
            }

            @Override
            public void onLoadFail(String packageName, IPluginViewType type, String failReason) {
                CCLog.i("PluginCardItemView", "load plugin fail:" + packageName + "---failReason:" + failReason);
                pluginIsLoaded = true;
                hasPlugin = "no";
                if (FAIL_REASON_LOAD_APK_ERROR.equals(failReason)) {
                    mReason = "未安装";
                } else if (FAIL_REASON_LOAD_INTERFACE_ERROR.equals(failReason)) {
                    mReason = "已安装没插件";
                }
                setDefaultView();
                ThreadManager.getInstance().uiThread(mPluginShowRunnable, 300);
            }

        }, new IBoundaryCallback() {
            @Override
            public boolean onTop(View leaveView) {
                return onBoundary(leaveView, IDirection.TOP);
            }

            @Override
            public boolean onLeft(View leaveView) {
                return onBoundary(leaveView, IDirection.LEFT);
            }

            @Override
            public boolean onRight(View leaveView) {
                return onBoundary(leaveView, IDirection.RIGHT);
            }

            @Override
            public boolean onDown(View leaveView) {
                return onBoundary(leaveView, IDirection.DOWN);
            }

            @Override
            public boolean onBackKey(View leaveView) {
                return onBoundary(leaveView, IDirection.BACK);
            }
        });
    }

    private void setContentView(View view) {
        LayoutParams params = new LayoutParams(Util.Div(410), Util.Div(540));
        params.gravity = Gravity.CENTER;
        mContentLayout.addView(view, params);
        mPoster.setVisibility(GONE);
    }

    private void setDefaultView() {
        if (mDefaultFocusView == null) {
            mDefaultFocusView = new View(getContext());
            mDefaultFocusView.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Div(18)).setSolidVisible(false));
            LayoutParams params = new LayoutParams(Util.Div(410 + 10), Util.Div(540 + 10));
            params.gravity = Gravity.CENTER;
            mContentLayout.addView(mDefaultFocusView, params);
        }
        mDefaultFocusView.setVisibility(INVISIBLE);
        setFocusable(true);
        setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                if (b) {
                    mDefaultFocusView.setVisibility(VISIBLE);
                } else {
                    mDefaultFocusView.setVisibility(INVISIBLE);
                }
                Util.focusAnimate(PluginCardItemView.this, b);
            }
        });
        setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    return onKeyDown(PluginCardItemView.this, keyCode);
                }
                return false;
            }
        });
        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                String reason = "已安装没插件";
                try {
                    Intent intent = getContext().getPackageManager().getLaunchIntentForPackage(mCardData.pkg);
                    getContext().startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        reason = "未安装";
                        Intent appstore = new Intent("coocaa.intent.action.APP_STORE_DETAIL");
                        appstore.putExtra("id", mCardData.pkg);
                        getContext().startActivity(appstore);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
                Map<String, String> params = new HashMap<>();
                params.put("plugin_name", mCardData.cardName);
                params.put("reason", reason);
                PluginLogger.submit(LogSDK.EVENT_ID_NO_PLUGIN_CLICK, params);
            }
        });
        mPoster.setVisibility(VISIBLE);
        if (EmptyUtils.isNotEmpty(mCardData.poster)) {
            ImageLoader.getLoader().with(getContext()).load(mCardData.poster).resize(Util.Div(410), Util.Div(540)).setScaleType(ImageView.ScaleType.FIT_XY)
                    .setLeftTopCorner(ITEM_CORNER).setLeftBottomCorner(ITEM_CORNER).setRightTopCorner(ITEM_CORNER).setRightBottomCorner(ITEM_CORNER).into(mPoster);
        }
    }

    @Override
    public boolean getFocus() {
        if (hasFocusable()) {
            return requestFocus();
        }
        return false;
    }

    @Override
    public void onShow() {
        super.onShow();
        ThreadManager.getInstance().uiThread(mPluginShowRunnable, 300);
    }

    @Override
    public void onHide() {
        super.onHide();
        ThreadManager.getInstance().removeUiThread(mPluginShowRunnable);
    }

    @Override
    public void onResume() {
        super.onResume();
        log("onResume:" + mCardData.pkg);
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        log("onPause:" + mCardData.pkg);
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onPause();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        log("onStop:" + mCardData.pkg);
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onStop();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        log("onDestroy:" + mCardData.pkg);
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onDestroy();
        }
    }

    private void log(String msg) {
        CCLog.i("PluginCardItemView", msg);
    }
}
