package com.smarthome.plugin.page.source;

import android.content.Context;
import android.support.v7.widget.NewRecycleAdapter;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.ccos.tvlauncher.sdk.IDirection;
import com.ccos.tvlauncher.sdk.sourcepage.ISourcePlugin;
import com.ccos.tvlauncher.sdk.sourcepage.ISourceView;
import com.ccos.tvlauncher.sdk.sourcepage.OnSourceBoundaryListener;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnBoundaryListener;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;
import com.smarthome.common.utils.ErrorView;
import com.smarthome.plugin.util.PluginUtil;
import com.smarthome.plugin.page.base.DeviceItemData;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/7
 */
public class SourcePluginView extends FrameLayout implements OnItemClickListener, OnItemFocusChangeListener, OnBoundaryListener {

    private NewRecycleLayout<DeviceItemData> mRecycleLayout;
    private NewRecycleAdapter<DeviceItemData> mAdapter;
    private List<DeviceItemData> mDataList = new ArrayList<>();
    private OnSourceBoundaryListener mBoundaryListener;
    private ISourcePlugin mSourcePlugin;
    private ISourceView mISourceView;
    private int mPosition;
    private ErrorView mErrorView;

    public SourcePluginView(Context context) {
        super(context);
        mRecycleLayout = new NewRecycleLayout<>(context);
        mRecycleLayout.setOrientation(LinearLayout.VERTICAL);
        mRecycleLayout.setSpanCount(1);
        mRecycleLayout.setmItemFocusChangeListener(this);
        mRecycleLayout.setmBoundaryListener(this);
        mRecycleLayout.setmItemClickListener(this);
        mRecycleLayout.setItemSpace(0, 0);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(880));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mRecycleLayout, params);
    }

    public void setCallback(OnSourceBoundaryListener boundaryListener, ISourcePlugin plugin, int position) {
        this.mBoundaryListener = boundaryListener;
        this.mSourcePlugin = plugin;
        this.mPosition = position;
    }

    public void setISourceView(ISourceView view) {
        mISourceView = view;
    }

    public void refreshUI(List<DeviceItemData> dataList) {
        if (mErrorView != null) {
            mErrorView.setVisibility(GONE);
        }
        mRecycleLayout.setVisibility(VISIBLE);
        mDataList.clear();
        mDataList.addAll(dataList);
        if (mAdapter == null) {
            mAdapter = new NewRecycleAdapter<DeviceItemData>(mDataList, 1) {
                @Override
                public NewRecycleAdapterItem<DeviceItemData> onCreateItem(Object type) {
                    if (type.toString().equals(DeviceItemData.ITEM_TYPE_ADD)) {
                        return new AddDeviceItemView(getContext());
                    } else {
                        return new SourceDeviceItemView(getContext());
                    }
                }

                @Override
                public Object getItemType(DeviceItemData deviceItemData) {
                    return deviceItemData.itemType;
                }
            };
            mRecycleLayout.setRecyclerAdapter(mAdapter);
        } else {
            mRecycleLayout.notifyDataSetChanged();
        }
    }

    public void notifyDeviceItemChanged(DeviceItemData updateData) {
        try {
            for (int i = 0; i < mDataList.size(); i++) {
                DeviceItemData data = mDataList.get(i);
                if (updateData.device_id.equals(data.device_id)) {
                    mDataList.set(i, updateData);
                    mAdapter.notifyItemChanged(i);
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showErrorView(String msg, String btnText, OnClickListener onClickListener) {
        mRecycleLayout.setVisibility(GONE);
        if (mErrorView == null) {
            mErrorView = new ErrorView(getContext());
            LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.gravity = Gravity.CENTER;
            addView(mErrorView, params);
        }
        mErrorView.setVisibility(VISIBLE);
        mErrorView.setErrorMsg(msg).setBtnText(btnText).setButtonClickListener(onClickListener);
    }

    public boolean getFocus() {
        if (mRecycleLayout.getChildItemCount() > 0) {
            mRecycleLayout.setSelection(0);
            return true;
        }
        return false;
    }

    @Override
    public void click(View view, int i) {
        mISourceView.dismissDialog();
        if (view instanceof SourceDeviceItemView) {
            PluginUtil.startSmartHome(getContext(), "source", mDataList.get(i));
        } else if (view instanceof AddDeviceItemView) {
            PluginUtil.gotoAddDevice(getContext());
        }
    }

    @Override
    public void focusChange(View view, int i, boolean b) {
        if (view instanceof SourceDeviceItemView) {
            ((SourceDeviceItemView) view).onFocusChange(view, b);
        } else if (view instanceof AddDeviceItemView) {
            ((AddDeviceItemView) view).onFocusChange(view, b);
        }
    }

    @Override
    public boolean onLeftBoundary(View view, int i) {
        return mBoundaryListener.onSourcePluginBoundary(mSourcePlugin, mPosition, IDirection.LEFT);
    }

    @Override
    public boolean onTopBoundary(View view, int i) {
        return mBoundaryListener.onSourcePluginBoundary(mSourcePlugin, mPosition, IDirection.TOP);
    }

    @Override
    public boolean onDownBoundary(View view, int i) {
        return mBoundaryListener.onSourcePluginBoundary(mSourcePlugin, mPosition, IDirection.DOWN);
    }

    @Override
    public boolean onRightBoundary(View view, int i) {
        return mBoundaryListener.onSourcePluginBoundary(mSourcePlugin, mPosition, IDirection.RIGHT);
    }

    @Override
    public boolean onOtherKeyEvent(View v, int position, int keyCode) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return mBoundaryListener.onSourcePluginBoundary(mSourcePlugin, mPosition, IDirection.BACK);
        }
        return false;
    }
}
