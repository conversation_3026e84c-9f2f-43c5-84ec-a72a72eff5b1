package com.smarthome.plugin.model;

import android.content.Context;
import android.content.pm.PackageManager;

import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.config.ConfigFactory;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/1
 */
public class PluginCacheData {

    /**
     * 从智慧家庭SharedPreferences缓存中获取familyId
     *
     * @param pluginContext
     * @return
     */
    public static String getFamilyId(Context pluginContext) {
        String family_id = "";
        try {
            Context context = pluginContext.createPackageContext(Constants.SMART_HOME_PKG, Context.CONTEXT_IGNORE_SECURITY);
            family_id = ConfigFactory.get(context, Constants.SP_NAME_SMARTHOME).get(Constants.SP_KEY_FAMILY_ID, "");
            CCLog.i("plugin load family_id: " + family_id);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return family_id;
    }

    /**
     * 从智慧家庭SharedPreferences缓存中获取screenId
     *
     * @param pluginContext
     * @return
     */
    public static String getScreenId(Context pluginContext) {
        String screen_id = "";
        try {
            Context context = pluginContext.createPackageContext(Constants.SMART_HOME_PKG, Context.CONTEXT_IGNORE_SECURITY);
            screen_id = ConfigFactory.get(context, Constants.SP_NAME_SMARTHOME).get(Constants.SP_KEY_SCREEN_ID, "");
            CCLog.i("plugin load screen_id: " + screen_id);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return screen_id;
    }
}
