package com.smarthome.plugin.page.main.deviceitem;

import android.content.Context;

import com.smarthome.plugin.page.base.BaseItemView;
import com.smarthome.plugin.page.base.DeviceItemData;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public abstract class BaseDeviceItemView extends BaseItemView<List<DeviceItemData>> {

    protected List<DeviceItemData> mDataList = new ArrayList<>();

    public BaseDeviceItemView(Context context, int pos) {
        super(context, pos);
    }

    @Override
    public void refreshUI(List<DeviceItemData> list) {
        mDataList.clear();
        this.mDataList.addAll(list);
    }

    public abstract boolean itemHasFocus();

    public void notifyDeviceItemChanged(DeviceItemData updateData) {

    }
}
