package com.smarthome.plugin.aiotdata;

import android.content.Context;

import com.alibaba.fastjson.JSONObject;
import com.smarthome.common.account.AccountInfo;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.plugin.account.PluginAccount;
import com.smarthome.plugin.model.DeviceBean;
import com.smarthome.plugin.model.SmartHomeHttpConfig;
import com.swaiot.lib.SkyAIOTCallback;
import com.swaiot.lib.SkyAIOTContract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AIOTDataModel
 * @Author: AwenZeng
 * @CreateDate: 2020/6/30 16:36
 * @Description:
 */
public class AIOTDataModel implements IAIOTDataModel {

    private Context mContext;
    private List<IAiotCallback> mAiotCallbacks = new ArrayList<>();
    private final static String DEVICE_LIST = "device_list";
    private final static String COMMAND_ID = "AIOTDataPlugin";

    private SkyAIOTCallback mLibCallback = new SkyAIOTCallback() {
        @Override
        public void require_app_operate(String s, String s1) {

        }

        @Override
        public void on_object_updated(String s, String s1, String s2) {

        }

        @Override
        public void on_resource_data(int code, String msg, String resource_type, String data, String commandId) {
            CCLog.d("on_resource_data() called with: code = [" + code + "], msg = [" + msg + "], resource_type = [" + resource_type + "], data = [" + data + "], commandId = [" + commandId + "]");
            try {
                if (resource_type.equals(DEVICE_LIST)) {
                    List<DeviceBean> list = null;
                    if (EmptyUtils.isNotEmpty(data)) {
                        list = JSONObject.parseArray(data, DeviceBean.class);
                    }
                    for (IAiotCallback callback : mAiotCallbacks) {
                        callback.onDeviceList(list);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void on_task_status(String s, String s1, String s2, String s3) {

        }

        @Override
        public void on_control_result(int i, String s, String s1, String s2, String s3, String s4) {

        }
    };

    @Override
    public void init(Context context) {
        CCLog.d("init() called");
        try {
            mContext = context;
            AccountInfo accountInfo = PluginAccount.getInstance().getAccountInfo();
            String userid = "";
            String token = "";
            if (accountInfo != null) {
                if (EmptyUtils.isNotEmpty(accountInfo.mobile)) {
                    userid = accountInfo.user_id;
                    token = accountInfo.token;
                }
            }
            String activeId = SalImpl.getSAL(mContext).getActiveID();
            Map<String, Object> options = new HashMap<>();
            options.put("storage_path", mContext.getFilesDir().getPath());
            options.put("filter_device_list", new String[]{activeId});
            SkyAIOTContract.do_init(
                    userid == null ? "" : userid,
                    token == null ? "" : token,
                    SmartHomeHttpConfig.APP_KEY,
                    SmartHomeHttpConfig.SECRET,
                    JSONObject.toJSONString(options),
                    mLibCallback);
            String[] watchs = new String[]{DEVICE_LIST};
            SkyAIOTContract.watch_resources(watchs);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void registerAiotCallback(IAiotCallback callback) {
        if (callback != null && !mAiotCallbacks.contains(callback)) {
            mAiotCallbacks.add(callback);
        }
    }

    @Override
    public void unRegisterAiotCallback(IAiotCallback callback) {
        if (callback != null && mAiotCallbacks.contains(callback)) {
            mAiotCallbacks.remove(callback);
        }
    }

    @Override
    public void getDeviceList(String familyId) {
        CCLog.d("getDeviceList() called with: familyId = [" + familyId + "]");
        if (EmptyUtils.isNotEmpty(familyId)) {
            SkyAIOTContract.require_resource(DEVICE_LIST, COMMAND_ID,(byte)0);
            SkyAIOTContract.control_object("set_current_family", familyId, "", COMMAND_ID);
        } else {
            SkyAIOTContract.require_resource(DEVICE_LIST, COMMAND_ID,(byte)1);
        }
    }
}
