package com.smarthome.plugin.model;

import com.smarthome.common.model.SmartBaseData;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/10
 */
public interface SmartHomeHttpMethod {


    @GET("smarthome/v1/panel-config/get-by-name")
    Call<SmartBaseData<OperationCardBean>> getOperationCardList(@Query("appkey") String appkey,
                                                                @Query("time") String time,
                                                                @Query("uid") String uid,
                                                                @Query("ak") String ak,
                                                                @Query("vuid") String vuid,
                                                                @Query("name") String name,
                                                                @Query("sign") String sign);
}
