package com.smarthome.plugin.page.main.deviceitem;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.skyworth.util.imageloader.OnBitmapLoadListener;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.page.base.DeviceItemData;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @Description: 运营卡片内部设备itemview
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public class SmallDeviceItemView extends FrameLayout {

    public View mBgView;
    public View mIcon;
    public TextView mDeviceName;
    public TextView mStatus;
    public DeviceItemData mData;

    public SmallDeviceItemView(Context context) {
        super(context);
        setFocusable(true);
        addBg();
        addIcon();
        addName();
        addStatus();
    }

    public void addBg() {
        mBgView = new View(getContext());
        mBgView.setBackground(XThemeUtils.getDrawable(Color.parseColor("#10FFFFFF"), 0, 0, Util.Div(10)));
        LayoutParams params = new LayoutParams(Util.Div(350), Util.Div(100));
        params.gravity = Gravity.CENTER;
        addView(mBgView, params);
    }

    public void addIcon() {
        mIcon = ImageLoader.getLoader().getView(getContext());
        LayoutParams params = new LayoutParams(Util.Div(60), Util.Div(60));
        params.gravity = Gravity.CENTER_VERTICAL;
        params.leftMargin = Util.Div(25);
        addView(mIcon, params);
    }

    public void addName() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(100);
        params.topMargin = Util.Div(23);
        mDeviceName = new TextView(getContext());
        mDeviceName.setTextColor(Color.WHITE);
        mDeviceName.setTextSize(Util.Dpi(24));
        mDeviceName.getPaint().setFakeBoldText(true);
        addView(mDeviceName, params);
    }

    public void addStatus() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(100);
        params.topMargin = Util.Div(57);
        mStatus = new TextView(getContext());
        mStatus.setTextColor(Color.parseColor("#aaFFFFFF"));
        mStatus.setTextSize(Util.Dpi(20));
        addView(mStatus, params);
    }

    public void refreshUI(DeviceItemData data) {
        this.mData = data;
        if (data.is_virtual) {
            mDeviceName.setTextColor(Color.WHITE);
            mStatus.setTextColor(Color.parseColor("#aaFFFFFF"));
            mStatus.setText(data.device_position + " | 虚拟设备");
        } else {
            if (isOnline()) {
                //在线
                mDeviceName.setTextColor(Color.WHITE);
                mStatus.setTextColor(Color.parseColor("#aaFFFFFF"));
                if (data.acess_type == 3) {
                    mStatus.setText("红外可控");
                } else {
                    mStatus.setText(data.device_position + " | " + data.device_status_desc);
                }
            } else {
                //离线
                mDeviceName.setTextColor(Color.parseColor("#66FFFFFF"));
                mStatus.setTextColor(Color.parseColor("#66FFFFFF"));
                mStatus.setText(data.device_position + " | 离线");
            }
        }
        mDeviceName.setText(data.device_name);
        if (EmptyUtils.isNotEmpty(data.device_icon)) {
            loadIcon();
        }
        onFocusChange(this, hasFocus());
    }

    private boolean isOnline() {
        return mData.online_status == 1;
    }

    private void loadIcon() {
        mIcon.setBackground(null);
        ImageLoader.getLoader().with(getContext()).load(mData.device_icon).setScaleType(ImageView.ScaleType.FIT_XY)
                .getBitmap(new OnBitmapLoadListener() {
                    @Override
                    public void loadSuccess(final Bitmap bitmap, String s) {
                        FuncKt.runOnUiThread(new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                mIcon.setBackground(new BitmapDrawable(bitmap));
                                if (!isOnline() && mIcon.getBackground() != null) {
                                    mIcon.getBackground().setAlpha(102);
                                }
                                return Unit.INSTANCE;
                            }
                        });
                    }

                    @Override
                    public void loadFailed(String s) {

                    }
                });
    }

    public void onFocusChange(View view, boolean b) {
        if (b) {
            mBgView.setBackground(XThemeUtils.getDrawable(Color.parseColor("#FFFFFF"), 0, 0, Util.Div(10)));
            mDeviceName.setTextColor(Color.BLACK);
            mStatus.setTextColor(Color.BLACK);
        } else {
            mBgView.setBackground(XThemeUtils.getDrawable(Color.parseColor("#10FFFFFF"), 0, 0, Util.Div(10)));
            if (isOnline()) {
                mDeviceName.setTextColor(Color.WHITE);
                mStatus.setTextColor(Color.parseColor("#aaFFFFFF"));
            } else {
                mDeviceName.setTextColor(Color.parseColor("#66FFFFFF"));
                mStatus.setTextColor(Color.parseColor("#66FFFFFF"));
            }
        }
    }

    public void destroy() {

    }
}
