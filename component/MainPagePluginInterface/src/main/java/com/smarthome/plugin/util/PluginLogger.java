package com.smarthome.plugin.util;

import com.smarthome.plugin.account.PluginAccount;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/10
 */
public class PluginLogger {

    public static void submit(String eventID) {
        submit(eventID, new HashMap<String, String>());
    }

    public static void submit(String eventID, String key, String value) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put(key, value);
            PluginAccount.getInstance().getConnector().logger().baseEvent(eventID, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void submit(String eventID, Map<String, String> params) {
        try {
            PluginAccount.getInstance().getConnector().logger().baseEvent(eventID, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
