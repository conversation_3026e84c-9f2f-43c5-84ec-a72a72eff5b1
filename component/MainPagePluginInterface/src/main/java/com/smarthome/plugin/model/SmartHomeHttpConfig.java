package com.smarthome.plugin.model;


import com.coocaa.app.core.http.HttpServiceManager;
import com.smarthome.common.BuildConfig;
import com.smarthome.common.account.AccountInfo;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Md5;
import com.smarthome.plugin.account.PluginAccount;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 2018/4/17.
 */
public class SmartHomeHttpConfig {
    public static final String APP_KEY = "bf5527edc4da4a40be832f26e27adf32";//新
    public static final String SECRET = "b509ec6e10a84f56ab792023508e47ae";//新

    public static String getDeviceServer() {
        return BuildConfig.DEBUG ? "https://api-test.skyworthiot.com/" : "https://api.skyworthiot.com/";
    }

    public static String getSign(Map<String, String> map) {
        String sign = sign(map, SECRET);
        CCLog.d("sign", "sign:" + sign);
        return sign;
    }

    public static Map<String, String> getBaseUrlParams() {
        Map<String, String> map = new HashMap<>();
        map.put("appkey", APP_KEY);
        map.put("time", String.valueOf(System.currentTimeMillis() / 1000));
        AccountInfo userInfo = PluginAccount.getInstance().getAccountInfo();
        map.put("uid", userInfo == null ? "" : userInfo.user_id);
        map.put("ak", userInfo == null ? "" : userInfo.token);
        map.put("vuid", SalImpl.getSAL(PluginAccount.getInstance().getContext()).getMAC());
        return map;
    }

    private static String sign(Map<String, String> map, String secret) {
        List<String> keys = new ArrayList<String>(map.keySet());
        Collections.sort(keys);

        String temStr = "";
        for (String key : keys) {
            temStr += key + map.get(key);
        }
        CCLog.d("sign", " temStr:" + temStr);
        String mysign = "";
        try {
            mysign = Md5.getMd5(temStr + secret);
        } catch (Exception e) {

        }
        CCLog.d("sign", " mysign:" + mysign);
        return mysign;
    }

    public static final HttpServiceManager.HeaderLoader SMARTHOME_HEADER_LOADER = new MyHeaderLoader();

    public static class MyHeaderLoader implements HttpServiceManager.HeaderLoader {

        Map<String, String> DEFAULT_HEADERS = null;

        private Map<String, String> loadHeader() {
            DEFAULT_HEADERS = new HashMap<>();
            Map<String, String> headerMap = PluginAccount.getInstance().getHeader();
            if (headerMap != null && headerMap.size() > 0) {
                DEFAULT_HEADERS.putAll(headerMap);
            }
            return DEFAULT_HEADERS;
        }

        @Override
        public synchronized Map<String, String> getHeader() {
            if (DEFAULT_HEADERS == null || DEFAULT_HEADERS.size() < 1) return loadHeader();
            return DEFAULT_HEADERS;
        }

        @Override
        public synchronized void updateHeader() {
            DEFAULT_HEADERS = null;
            getHeader();
        }
    }

}
