package com.smarthome.plugin.page.main.carditem;

import android.content.Context;
import android.os.Bundle;

import com.alibaba.fastjson.JSONObject;
import com.ccos.tvlauncher.sdk.IPluginConnector;
import com.coocaa.app.core.utils.FuncKt;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.plugin.account.PluginAccount;
import com.smarthome.plugin.aiotdata.IAIOTDataModel;
import com.smarthome.plugin.aiotdata.IAiotCallback;
import com.smarthome.plugin.model.DevcieStatusBean;
import com.smarthome.plugin.model.DeviceBean;
import com.smarthome.plugin.model.PluginCacheData;
import com.smarthome.plugin.page.base.DeviceItemData;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/24
 */
public class DeviceCardPresenter implements IAiotCallback {
    private Context mContext;
    private DeviceCardItemView mView;
    private String mScreenId;
    private List<DeviceItemData> mDeviceList = new ArrayList<>();
    private IPluginConnector.UserChangeListener mUserChangeListener = new IPluginConnector.UserChangeListener() {
        @Override
        public void onUserChanged() {
            log("onUserChanged hasLogin:" + PluginAccount.getInstance().hasLogin());
            IAIOTDataModel.INSTANCE.init(mContext);
            loadData();
        }
    };

    public DeviceCardPresenter(Context context, DeviceCardItemView view) {
        mContext = context;
        mView = view;
        IAIOTDataModel.INSTANCE.registerAiotCallback(this);
        PluginAccount.getInstance().registerUserChangeListener(mUserChangeListener);
    }

    public void loadData() {
        if (!Android.isNetConnected(mContext)) {
            return;
        }
        mScreenId = PluginCacheData.getScreenId(mContext);
        if (PluginAccount.getInstance().hasLogin()) {
            if (PluginAccount.getInstance().isBindMobile()) {
                String familyId = PluginCacheData.getFamilyId(mContext);
                IAIOTDataModel.INSTANCE.getDeviceList(familyId);
            } else {
                log("loadData unbind mobile.");
                mView.setDefaultView();
            }
        } else {
            log("loadData unlogin.");
            mView.setDefaultView();
        }
    }

    public void pushCallback(Bundle bundle) {
        String type = bundle.getString("PUSH_TYPE");
        String data = bundle.getString("PUSH_DATA");
        log("pushCallback type:" + type + "--data:" + data);
        if (EmptyUtils.isNotEmpty(type)) {
            switch (type) {
                case "DEVICE_STATUS":
                    if (EmptyUtils.isNotEmpty(data)) {
                        notifyDeviceItemChanged(data);
                    }
                    break;
                case "DEVICE_LIST":
                    if (EmptyUtils.isNotEmpty(data)) {
                        onDeviceList(JSONObject.parseArray(data, DeviceBean.class));
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private void notifyDeviceItemChanged(String data) {
        try {
            DevcieStatusBean devcieStatusBean = JSONObject.parseObject(data, DevcieStatusBean.class);
            if (devcieStatusBean != null) {
                for (final DeviceItemData itemData : mDeviceList) {
                    if (itemData.device_id.equals(devcieStatusBean.device_id)) {
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.device_name)) {
                            itemData.device_name = devcieStatusBean.device_name;
                        }
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.online_status)) {
                            itemData.online_status = Integer.parseInt(devcieStatusBean.online_status);
                        }
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.device_status_desc)) {
                            itemData.device_status_desc = devcieStatusBean.device_status_desc;
                        }
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.status)) {
                            JSONObject reportStatus = JSONObject.parseObject(itemData.report_status);
                            JSONObject updateStatus = JSONObject.parseObject(devcieStatusBean.status);
                            Object[] updateStatusArr = updateStatus.keySet().toArray();
                            if (EmptyUtils.isNotEmpty(updateStatusArr)) {
                                for (Object key : updateStatusArr) {
                                    reportStatus.put((String) key, updateStatus.getString((String) key));
                                }
                            }
                            itemData.report_status = reportStatus.toJSONString();
                        }
                        log("notifyDeviceItemChanged:" + data);
                        FuncKt.runOnUiThread(new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                mView.notifyDeviceItemChanged(itemData);
                                return Unit.INSTANCE;
                            }
                        });
                        break;
                    }
                }
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDeviceList(List<DeviceBean> list) {
        if (!PluginAccount.getInstance().hasLogin() || !PluginAccount.getInstance().isBindMobile()) {
            log("onDeviceList unlogin or unbind mobile.");
            mView.setDefaultView();
            return;
        }
        mDeviceList.clear();
        if (EmptyUtils.isNotEmpty(list)) {
            for (DeviceBean bean : list) {
                if (bean.is_virtual) {
                    break;
                }
                //过滤本机设备
                if (EmptyUtils.isNotEmpty(mScreenId) && bean.device_id.equals(mScreenId)) {
                    continue;
                }
                DeviceItemData data = new DeviceItemData();
                data.copy(bean);
                mDeviceList.add(data);
            }
        }
        log("onDeviceList: mDeviceList.size:" + list.size());
        if (EmptyUtils.isNotEmpty(mDeviceList)) {
            FuncKt.runOnUiThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    mView.refreshUI(mDeviceList);
                    return Unit.INSTANCE;
                }
            });
        } else {
            mView.setDefaultView();
        }
    }

    private void log(String msg) {
        CCLog.i("DeviceCardPresenter:" + msg);
    }

    public void onDestroy() {
        IAIOTDataModel.INSTANCE.unRegisterAiotCallback(this);
        PluginAccount.getInstance().unregisterUserChangeListener(mUserChangeListener);
    }
}
