package com.smarthome.plugin.page.main;

import android.app.Activity;
import android.os.Bundle;
import android.view.WindowManager;

import com.ccos.tvlauncher.sdk.BaseTvLauncherPlugin;
import com.ccos.tvlauncher.sdk.PluginContentData;
import com.coocaa.uisdk.presenter.PresenterFactoryV8;
import com.coocaa.uisdk.utils.ImageLoaderV8;
import com.coocaa.uisdk.utils.SupportUtilV8;
import com.skyworth.framework.skysdk.ipc.SkyApplication;
import com.skyworth.smarthome_tv.pluginmanager.PluginManager;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.aiotdata.IAIOTDataModel;
import com.smarthome.plugin.account.PluginAccount;
import com.smarthome.plugin.page.main.custom.PluginPresenterFactory;
import com.smarthome.plugin.util.HomeImgLoader;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/4/26
 */
public class SmartHomeMainPlugin extends BaseTvLauncherPlugin {
    private final static String TAG = "SmartHomeMainPlugin";
    private SmartHomePluginView contentView;
//    private SmartHomePluginShortCutView mShortCutView;

    @Override
    public void onInit() {
        CCLog.init(TAG);
        init();
        CCLog.i("onInit: needContentView:" + needContentView);
        if (needContentView) {
            createContentView();
        }
//        createShortcutView();
    }

    private void init() {
        try {
            SkyApplication.init(pluginContext);
            PluginAccount.getInstance().init(pluginContext, connector);
            PluginAccount.getInstance().setHeader(header);
            IAIOTDataModel.INSTANCE.init(pluginContext);
            Util.instence(pluginContext);
            XThemeUtils.init(pluginContext);
            ImageLoader.getLoader().init(pluginContext);
            initUISDKV8();
            initPluginManager();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initUISDKV8() {
        ImageLoaderV8.setImageLoader(new HomeImgLoader());
        SupportUtilV8.setContext(pluginContext);
        SupportUtilV8.setIsSupportRounded(ImageLoader.isSupportRoundCorner());
        PresenterFactoryV8.getInstance().addExtraFactory(new PluginPresenterFactory());
    }

    private void initPluginManager() {
        WindowManager windowManager = null;
        if (pluginContext instanceof Activity) {
            windowManager = ((Activity) pluginContext).getWindowManager();
        }
        PluginManager.getInstance().init(pluginContext, windowManager);
        PluginManager.getInstance().setConnector(connector);
    }

    private void createContentView() {
        if (contentView == null) {
            contentView = new SmartHomePluginView(pluginContext);
            contentView.setCallback(boundaryCallback, this);
        }
        contentView.create(pluginContentData);
        callback.onContentViewCreated(SmartHomeMainPlugin.this, contentView);
    }

//    private void createShortcutView() {
//        if (EmptyUtils.isEmpty(mShortCutView)) {
//            mShortCutView = new SmartHomePluginShortCutView(pluginContext);
//            mShortCutView.setCallback(boundaryCallback, this);
//        }
//        callback.onShortcutViewCreated(SmartHomeMainPlugin.this, mShortCutView);
//    }

    @Override
    public void onContentDataChanged(PluginContentData contentData) {
        super.onContentDataChanged(contentData);
        this.pluginContentData = contentData;
        CCLog.i("onContentDataChanged: " + contentData.toString());
        if (needContentView) {
            createContentView();
            callback.onContentViewCreated(this, contentView);
            contentView.onLayoutShow();
        }
    }

    @Override
    public String productId() {
        return LogSDK.PRODUCT_ID;
    }

    @Override
    public boolean contentObtainFocus() {
        CCLog.i("contentObtainFocus: ");
        return contentView != null && contentView.obtainFocus();
    }

    @Override
    public boolean shortcutObtainFocus() {
        CCLog.i("shortcutObtainFocus: ");
//        return mShortCutView.obtainFocus();
        return false;
    }

    @Override
    public void onDeliverPluginMessage(Bundle bundle) {
        super.onDeliverPluginMessage(bundle);
        CCLog.i("onDeliverPluginMessage: ");
        if (bundle != null) {
            PluginManager.getInstance().onDeliverPluginMessage(bundle);//回调给插件
        }
        if (contentView != null) {
            contentView.onDeliverPluginMessage(bundle);
        }
    }

    @Override
    public void onShortcutStateChanged(int state) {
        super.onShortcutStateChanged(state);
        CCLog.i("onShortcutStateChanged: " + state);
        PluginManager.getInstance().onShortcutStateChanged(state);
    }

    @Override
    public void setFocusColor(int color) {
        SupportUtilV8.setBlockFocusColor(color);
    }

    @Override
    public void setPageChangeDirection(int direction) {
        super.setPageChangeDirection(direction);
        CCLog.i("setPageChangeDirection: " + direction);
        if (contentView != null) {
            contentView.setPageChangeDirection(direction);
        }
    }

    @Override
    public void onShow() {
        CCLog.i("onShow: ");
        if (contentView != null) {
            contentView.onShow();
        }
    }

    @Override
    public void onHide() {
        CCLog.i("onHide: ");
        if (contentView != null) {
            contentView.onHide();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        CCLog.i("onResume: ");
        if (contentView != null) {
            contentView.onResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        CCLog.i("onPause: ");
        if (contentView != null) {
            contentView.onPause();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        CCLog.i("onStop: ");
        if (contentView != null) {
            contentView.onStop();
        }
    }

    @Override
    public void onDestroy() {
        CCLog.i("onDestroy: ");
        if (contentView != null) {
            contentView.onDestroy();
        }
        PluginAccount.getInstance().removeUserChangeListener();
    }
}
