package com.smarthome.plugin.page.main.carditem;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.model.OperationCardBean;
import com.smarthome.plugin.util.PluginLogger;
import com.smarthome.plugin.util.PluginUtil;
import com.smarthome.plugin.page.main.deviceitem.BaseDeviceItemView;
import com.smarthome.plugin.page.main.deviceitem.SingleDeviceItemView;
import com.smarthome.plugin.page.main.deviceitem.MultiDeviceItemView;
import com.smarthome.plugin.page.base.DeviceItemData;
import com.swaiot.aiotlib.common.util.ThreadManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public class DeviceCardItemView extends BaseCardItemView<List<DeviceItemData>> implements View.OnFocusChangeListener {
    private View mBgView;
    private View mFocusView;
    private TextView mTitle;
    private BaseDeviceItemView mItemView;
    private OperationCardBean.CardData mCardData;
    private DeviceCardPresenter mDevicePresenter;

    private IDeviceCardLayout mIDeviceCardLayout = new IDeviceCardLayout() {
        @Override
        public void onFocusChange(boolean b) {
            DeviceCardItemView.this.onFocusChange(DeviceCardItemView.this, b);
        }
    };

    private Runnable mPluginShowRunnable = new Runnable() {
        @Override
        public void run() {
            Map<String, String> p = new HashMap<>();
            p.put("plugin_name", mCardData.cardName);
            p.put("if_plugin", "yes");
            PluginLogger.submit(LogSDK.EVENT_ID_PLUGIN_SHOW, p);
        }
    };

    public DeviceCardItemView(Context context, int pos, OperationCardBean.CardData data) {
        super(context, pos);
        setFocusable(true);
        setOnFocusChangeListener(this);
        mCardData = data;
        mBgView = new View(context);
        mBgView.setBackground(XThemeUtils.getDrawable(Color.parseColor("#3F444E"), 0, 0, ITEM_CORNER));
        LayoutParams params = new LayoutParams(Util.Div(410), Util.Div(540));
        params.gravity = Gravity.CENTER;
        mContentLayout.addView(mBgView, params);
        mBgView.setVisibility(GONE);
        mFocusView = new View(context);
        mFocusView.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Div(18)).setSolidVisible(false));
        params = new LayoutParams(Util.Div(410 + 10), Util.Div(540 + 10));
        params.gravity = Gravity.CENTER;
        mContentLayout.addView(mFocusView, params);
        mFocusView.setVisibility(INVISIBLE);

        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(58);
        params.topMargin = Util.Div(30);
        mTitle = new TextView(getContext());
        mTitle.setTextColor(Color.WHITE);
        mTitle.setTextSize(Util.Dpi(28));
        mTitle.getPaint().setFakeBoldText(true);
        mTitle.setText("智能设备");
        mContentLayout.addView(mTitle, params);
        mTitle.setVisibility(INVISIBLE);

        mDevicePresenter = new DeviceCardPresenter(context, this);
        mDevicePresenter.loadData();

        this.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    return onKeyDown(DeviceCardItemView.this, keyCode);
                }
                return false;
            }
        });
        setOnClick(null);
        mPoster.setVisibility(VISIBLE);
        mPoster.bringToFront();
        if (EmptyUtils.isNotEmpty(mCardData.poster)) {
            ImageLoader.getLoader().with(getContext()).load(mCardData.poster).resize(Util.Div(410), Util.Div(540)).setScaleType(ImageView.ScaleType.FIT_XY)
                    .setLeftTopCorner(ITEM_CORNER).setLeftBottomCorner(ITEM_CORNER).setRightTopCorner(ITEM_CORNER).setRightBottomCorner(ITEM_CORNER).into(mPoster);
        }
    }

    private void setOnClick(final DeviceItemData data) {
        this.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                PluginUtil.startSmartHome(getContext(), data);
            }
        });
    }

    @Override
    public void refreshUI(final List<DeviceItemData> datas) {
        mPoster.setVisibility(GONE);
        mTitle.setVisibility(VISIBLE);
        mBgView.setVisibility(VISIBLE);
        boolean needFocus = false;
        if (currentItemHasFocus()) {
            needFocus = true;
            mTitle.setFocusable(true);
            mTitle.requestFocus();//临时抢到焦点
        } else {
            mTitle.setFocusable(false);
        }
        clearItemView();
        int size = datas.size();
        if (size == 1) {
            mItemView = new SingleDeviceItemView(getContext(), mCardPosition);
            this.setFocusable(true);
            setOnClick(datas.get(0));
        } else {
            setFocusable(false);
            mItemView = new MultiDeviceItemView(getContext(), mCardPosition, mIDeviceCardLayout);
            mItemView.setBoundaryCallback(mSmartHomeBoundaryCallback);
        }
        mItemView.refreshUI(datas);
        LayoutParams params = new LayoutParams(Util.Div(410), Util.Div(540));
//        params.topMargin = Util.Div(80);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        mContentLayout.addView(mItemView, params);
        if (needFocus) {
            getFocus();
        }
    }

    public DeviceCardPresenter getPresenter() {
        return mDevicePresenter;
    }

    public void notifyDeviceItemChanged(DeviceItemData updateData) {
        if (mItemView != null) {
            mItemView.notifyDeviceItemChanged(updateData);
        }
    }

    private boolean currentItemHasFocus() {
        return hasFocus() || (mItemView != null && mItemView.itemHasFocus());
    }

    public void setDefaultView() {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                setFocusable(true);
//                boolean needFocus = false;
                if (currentItemHasFocus()) {
//                    needFocus = true;
                    requestFocus();
                }
                clearItemView();
                mBgView.setVisibility(INVISIBLE);
                mTitle.setVisibility(INVISIBLE);
                mPoster.setVisibility(VISIBLE);
                mPoster.bringToFront();
//                if (needFocus) {
//                    getFocus();
//                }
                return Unit.INSTANCE;
            }
        });
    }

    private void clearItemView() {
        if (mItemView != null && mItemView.getParent() != null) {
            mContentLayout.removeView(mItemView);
            mItemView = null;
        }
    }

    @Override
    public boolean getFocus() {
        boolean ret;
        if (mItemView == null || mItemView instanceof SingleDeviceItemView) {
            this.setFocusable(true);
            ret = this.requestFocus();
        } else {
            onFocusChange(this, true);
            ret = mItemView.getFocus();
        }
        mTitle.setFocusable(false);
        return ret;
    }

    @Override
    public void onShow() {
        super.onShow();
        ThreadManager.getInstance().uiThread(mPluginShowRunnable, 300);
    }

    @Override
    public void onHide() {
        super.onHide();
        ThreadManager.getInstance().removeUiThread(mPluginShowRunnable);
    }

    @Override
    public void onResume() {
        super.onResume();
        mDevicePresenter.loadData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mDevicePresenter.onDestroy();
    }

    @Override
    public void onFocusChange(View view, boolean b) {
        if (b) {
            mFocusView.setVisibility(VISIBLE);
        } else {
            mFocusView.setVisibility(GONE);
        }
        Util.focusAnimate(view, b);
    }

    public interface IDeviceCardLayout {
        void onFocusChange(boolean b);
    }
}
