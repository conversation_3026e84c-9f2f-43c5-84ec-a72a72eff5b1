package com.smarthome.plugin.page.main.deviceitem;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.FinalCallback;
import com.skyworth.util.imageloader.ImageLoader;
import com.skyworth.util.imageloader.OnBitmapLoadListener;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.plugin.page.base.DeviceItemData;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public class SingleDeviceItemView extends BaseDeviceItemView {

    private View mIcon;
    private TextView mDeviceName;
    private TextView mStatus;

    public SingleDeviceItemView(Context context, int pos) {
        super(context, pos);
        addIcon();
        addName();
        addStatus();
    }

    private void addIcon() {
        mIcon = ImageLoader.getLoader().getView(getContext());
        LayoutParams params = new LayoutParams(Util.Div(250), Util.Div(250));
        params.topMargin = Util.Div(130);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mIcon, params);
    }

    private void addName() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(30);
        params.topMargin = Util.Div(444);
        mDeviceName = new TextView(getContext());
        mDeviceName.setTextColor(Color.WHITE);
        mDeviceName.setTextSize(Util.Dpi(32));
        mDeviceName.getPaint().setFakeBoldText(true);
        addView(mDeviceName, params);
    }

    private void addStatus() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(30);
        params.topMargin = Util.Div(486);
        mStatus = new TextView(getContext());
        mStatus.setTextColor(Color.parseColor("#aaFFFFFF"));
        mStatus.setTextSize(Util.Dpi(24));
        addView(mStatus, params);
    }

    @Override
    public void refreshUI(List<DeviceItemData> datas) {
        super.refreshUI(datas);
        setData(mDataList.get(0));
    }

    @Override
    public boolean itemHasFocus() {
        return false;
    }

    @Override
    public void notifyDeviceItemChanged(DeviceItemData updateData) {
        super.notifyDeviceItemChanged(updateData);
        mDataList.clear();
        mDataList.add(updateData);
        setData(updateData);
    }

    private void setData(final DeviceItemData data) {
        if (data.online_status == 1) {
            //在线
            mDeviceName.setTextColor(Color.WHITE);
            mStatus.setTextColor(Color.parseColor("#aaFFFFFF"));
            if (data.acess_type == 3) {
                mStatus.setText("红外可控");
            } else {
                mStatus.setText(data.device_position + " | " + data.device_status_desc);
            }
        } else {
            //离线
            mDeviceName.setTextColor(Color.parseColor("#66FFFFFF"));
            mStatus.setTextColor(Color.parseColor("#66FFFFFF"));
            mStatus.setText(data.device_position + " | 离线");
        }
        mDeviceName.setText(data.device_name);
        if (EmptyUtils.isNotEmpty(data.device_icon)) {
            mIcon.setBackground(null);
            ImageLoader.getLoader().with(getContext()).load(data.device_icon).resize(Util.Div(250), Util.Div(250)).setScaleType(ImageView.ScaleType.FIT_XY)
                    .getBitmap(new OnBitmapLoadListener() {
                        @Override
                        public void loadSuccess(final Bitmap bitmap, String s) {
                            FuncKt.runOnUiThread(new Function0<Unit>() {
                                @Override
                                public Unit invoke() {
                                    mIcon.setBackground(new BitmapDrawable(bitmap));
                                    if (data.online_status == 0 && mIcon.getBackground() != null) {
                                        mIcon.getBackground().setAlpha(102);
                                    }
                                    return Unit.INSTANCE;
                                }
                            });
                        }

                        @Override
                        public void loadFailed(String s) {

                        }
                    });
        }
    }

}
