apply plugin: 'com.android.library'

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILDTOOLS_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(":UISDK")
    implementation project(':Common')
    implementation project(":PluginManager")
    implementation project(":smarthome-aiot")
    compileOnly project(":TvLauncherPluginInterface")

    implementation 'swaiotos.ui:imageloader:1.0.57'
    implementation 'swaiotos.ui:common:1.0.57'
    implementation 'swaiotos.support:appcore:1.0.57'
}
