//if(rootProject.ext.useLib){
    apply plugin: 'com.android.library'
//}else{
//    apply plugin: 'com.android.application'
//}

android {
    compileSdkVersion gradle.ext.api
    buildToolsVersion gradle.ext.buildTools

    defaultConfig {
        minSdkVersion gradle.ext.minSdkVersion
        targetSdkVersion gradle.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
//    libraryVariants.all {
//        varinat ->
//            varinat.outputs.all {
//                outputFileName = 'ui.aar'
//            }
//    }

}

dependencies {
    implementation  "com.android.support:support-v4:$support_v4"
    implementation  "com.android.support:recyclerview-v7:$recyclerview_v7"
    implementation "com.alibaba:fastjson:$fastjson_version"
}
