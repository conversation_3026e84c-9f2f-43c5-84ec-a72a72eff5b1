apply plugin: 'com.android.library'

def ver_major = 1
def ver_minor = 0
def ver_build = getDate()

android {
    compileSdkVersion 28

    defaultConfig {
        minSdkVersion 15
        targetSdkVersion 28
        versionCode  Integer.valueOf(ver_build)
        versionName ver_major.toString() + "." + ver_minor.toString() + "." + ver_build

//        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
//        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    android.libraryVariants.all { variant ->
        if(variant.name.equalsIgnoreCase("release")) {
            variant.outputs.all { output ->
                def f = output.outputFileName
                if (f != null && f.endsWith('.aar')) {
                    def fileName = "iot-admin-channel-v${defaultConfig.versionName}.aar"
                    output.outputFileName = fileName
                }
            }
        }
    }
}

def getDate() {
    def date = new Date()
    def timeZone = TimeZone.getTimeZone("Asia/Shanghai")
    def formattedDate = date.format('yyMMddHHmm',timeZone)
    return formattedDate
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    api project(':iot-channel-sdk:sdk')
    implementation 'com.google.code.gson:gson:2.8.6'

    testImplementation 'junit:junit:4.12'
    implementation 'no.nordicsemi.android.support.v18:scanner:1.4.2'
}
