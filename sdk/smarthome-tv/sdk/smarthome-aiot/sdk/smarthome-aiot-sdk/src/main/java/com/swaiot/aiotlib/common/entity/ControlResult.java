package com.swaiot.aiotlib.common.entity;

public class ControlResult {

    /**
     * device_id : xxx
     * status : {"power":1,"mode":"auto","fan_speed":"low","temp":25,"target_temp":26}
     */

    private String device_id;
    private StatusBean status;

    public String getDevice_id() {
        return device_id;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public StatusBean getStatus() {
        return status;
    }

    public void setStatus(StatusBean status) {
        this.status = status;
    }

    public static class StatusBean {
        /**
         * power : 1
         * mode : auto
         * fan_speed : low
         * temp : 25
         * target_temp : 26
         */

        private int power;
        private String mode;
        private String fan_speed;
        private int temp;
        private int target_temp;

        public int getPower() {
            return power;
        }

        public void setPower(int power) {
            this.power = power;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public String getFan_speed() {
            return fan_speed;
        }

        public void setFan_speed(String fan_speed) {
            this.fan_speed = fan_speed;
        }

        public int getTemp() {
            return temp;
        }

        public void setTemp(int temp) {
            this.temp = temp;
        }

        public int getTarget_temp() {
            return target_temp;
        }

        public void setTarget_temp(int target_temp) {
            this.target_temp = target_temp;
        }
    }
}
