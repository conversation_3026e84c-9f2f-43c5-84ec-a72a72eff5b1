package com.swaiot.aiotlib.common.entity;

/**
 * Describe:已配网的设备
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/10
 */
public class DiscoverNetworkDevice {
    /**
     * device_id : ec83ea90009e1e9731d426051e677c96
     * product_type_id : 1
     * product_model : SmartTV
     * product_brand_id : 1
     * module_chip : 9S89-S8A
     * iot_cloud : 1
     * protocol_version : 1.2.0
     * mac_address : a4e61501486e
     */
    public String device_id;
    public String product_type_id;
    public String product_model;
    public String product_brand_id;
    public String module_chip;
    public String iot_cloud;
    public String protocol_version;
    public String mac_address;
    public String device_name;
    public String bind_status;
    public String product_type_logo;
    public String brand_cn;
    public String icon;
    public String nick_name;


    public DiscoverWifiDevice deviceInfo;//发现配网设备信息
}
