package com.swaiot.aiotlib.common.entity;

/**
 * Describe:发现智能设备信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/4
 */
public class DiscoverWifiDevice {
    private String pattern;
    private String password;

    private DiscoverDeviceDetail deviceDetail;

    private WifiInfo wifiInfo;

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public DiscoverDeviceDetail getDeviceDetail() {
        return deviceDetail;
    }

    public void setDeviceDetail(DiscoverDeviceDetail deviceDetail) {
        this.deviceDetail = deviceDetail;
    }

    public WifiInfo getWifiInfo() {
        return wifiInfo;
    }

    public void setWifiInfo(WifiInfo wifiInfo) {
        this.wifiInfo = wifiInfo;
    }
}
