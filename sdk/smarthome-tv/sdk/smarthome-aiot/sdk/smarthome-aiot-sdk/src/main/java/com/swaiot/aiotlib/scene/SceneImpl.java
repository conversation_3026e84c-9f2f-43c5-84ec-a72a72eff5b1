package com.swaiot.aiotlib.scene;

import android.os.IBinder;
import android.os.RemoteException;

import com.swaiot.aiotlib.BinderPool;
import com.swaiot.aiotlib.common.base.IResult;

/**
 * @ClassName: SceneImpl
 * @Author: Awen<PERSON>eng
 * @CreateDate: 2020/5/8 19:43
 * @Description:
 */
public class SceneImpl implements IScene {

    private BinderPool binderPool;
    private String commandId;

    public SceneImpl(BinderPool binderPool, String commandId) {
        this.binderPool = binderPool;
        this.commandId = commandId;
    }

    /**
     * 获取场景列表
     *
     * @param callBack 结果回调
     */
    @Override
    public void getSceneList(IScene.ISceneCallBack callBack) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_SCENE_LIST, callBack);
        IBinder binder = binderPool.queryBinder(BinderPool.SCENE_INFO);//获取Binder后使用
        ISceneInfo iSceneInfo = ISceneInfo.Stub.asInterface(binder);
        try {
            if(iSceneInfo!=null){
                iSceneInfo.getSceneList(commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 获取场景列表
     *
     * @param cmd     指令
     * @param sceneId 场景id
     * @param result  结果回调
     */
    @Override
    public void controlScene(String cmd, String sceneId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_CONTROL_SENCE, result);
        IBinder binder = binderPool.queryBinder(BinderPool.SCENE_CONTROL);//获取Binder后使用
        ISceneControl control = ISceneControl.Stub.asInterface(binder);
        try {
            if(control!=null){
                control.controlScene(cmd, sceneId, commandId);
            }

        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void cancelNewSceneMark(String scene_id, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_CANCEL_NEW_SCENE_MARK, result);
        IBinder binder = binderPool.queryBinder(BinderPool.SCENE_CONTROL);//获取Binder后使用
        ISceneControl control = ISceneControl.Stub.asInterface(binder);
        try {
            if(control!=null){
                control.cancelNewSceneMark(scene_id,commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加场景
     *
     * @param result 结果回调
     */
    @Override
    public void addScene(IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_SCENE_MANAGER, result);
        IBinder binder = binderPool.queryBinder(BinderPool.SCENE_MANAGER);//获取Binder后使用
        ISceneManager manager = ISceneManager.Stub.asInterface(binder);

        try {
            if(manager!=null){
                manager.addScene(commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 编辑场景
     *
     * @param sceneId 场景id
     * @param result  结果回调
     */
    @Override
    public void editScene(String sceneId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_EDIT_SCENE, result);
        IBinder binder = binderPool.queryBinder(BinderPool.SCENE_MANAGER);//获取Binder后使用
        ISceneManager manager = ISceneManager.Stub.asInterface(binder);

        try {
            if(manager!=null){
                manager.editScene(sceneId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 删除场景
     *
     * @param sceneId 场景id
     * @param result  结果回调
     */
    @Override
    public void deleteScene(String sceneId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_DELETE_SCENE, result);
        IBinder binder = binderPool.queryBinder(BinderPool.SCENE_MANAGER);//获取Binder后使用
        ISceneManager manager = ISceneManager.Stub.asInterface(binder);
        try {
            if(manager!=null){
                manager.editScene(sceneId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
}
