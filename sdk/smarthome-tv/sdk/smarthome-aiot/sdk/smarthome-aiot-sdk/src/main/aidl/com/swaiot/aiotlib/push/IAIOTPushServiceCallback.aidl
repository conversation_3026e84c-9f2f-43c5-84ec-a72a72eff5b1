// IAIOTPushServiceCallback.aidl
package com.swaiot.aiotlib.push;

// Declare any non-default types here with import statements

interface IAIOTPushServiceCallback {
   void onInit(String data);
   void onAccountChange();
   void onReceiveData(String event, String data);
   void onHandleDataCallback(String object_type, String device_id, String data);
   void onHttpCallBack(int code, String msg, String data, String resource_type, String commandId);

   void onApconfigProgress(int progress,int total,String extra);
   void onApconfigOk(String result);
   void onApconfigFail(int code,String erro);
   void onApconfigConnectNetFail(String wifiInfo);

   void onDiscoverWifiDevice(String result);
   void onDiscoverNetworkDevice(String result);

   void onSpecialVoiceHandle(String cmd);
}
