package com.swaiot.aiotlib.device;

import com.swaiot.aiotlib.common.base.IResult;
import com.swaiot.aiotlib.common.entity.BindStatusBean;
import com.swaiot.aiotlib.common.entity.ControlResult;
import com.swaiot.aiotlib.common.entity.DeviceBean;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: IDevice
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 19:39
 * @Description:
 */
public interface IDevice {

    /**
     * 获取设备列表结果回调
     */
    interface IDevicesCallBack {
        void onDevices(int code, String msg, List<DeviceBean> list);
    }

    /**
     * 控制指令回调
     */
    interface IControlResult {
        void onControlResult(int code, String msg, ControlResult result);
    }

    /**
     * 获取设备绑定列表结果回调
     */
    interface IBindStatusCallBack {
        void onBindStatus(int code, String msg, List<BindStatusBean> list);
    }

    /**
     * 获取设备绑定列表结果回调
     */
    interface IApconfigCallBack {
        void onApconfigProgress(int progress,int total,String extra);
        void onApconfigOk(String result);
        void onApconfigFail(int code,String erro);
        void onApconfigConnectNetFail(String wifiInfo);
    }


    /**
     * 获取设备列表，设备列表绑定在家庭下
     *
     * @param familyId 家庭id
     * @param callBack 结果回调
     */
    void getDeviceList(String familyId, IDevicesCallBack callBack);

    /**
     * 控制指定设备
     *
     * @param deviceId 家庭id
     * @param params   只需要传改变的属性值 如： {"mode": "auto"}
     * @param result   结果回调
     */
    void controlDevice(String deviceId, Map<String, String> params, IControlResult result);

    /**
     * 取消新设备标志
     * @param deviceId
     * @param result
     */
    void cancelNewDeviceMark(String deviceId,IControlResult result);

    /**
     * 绑定设备到指定的家庭id
     *
     * @param deviceId 设备id
     * @param familyId 家庭id
     * @param params   属性值
     * @param result   结果回调
     */
    void bindDevice(String deviceId, String familyId, Map<String, String> params, IResult result);


    /**
     * 局域网发现时批量获取局域网的设备绑定状态
     *
     * @param params   属性值
     * @param callBack 结果回调
     */
    void getBindStatus(Map<String, String> params,IBindStatusCallBack callBack);

    /**
     * 解绑设备
     *
     * @param deviceId 设备id
     * @param result   结果回调
     */
    void unBindDevice(String deviceId, IResult result);

    /**
     * 强制解绑设备
     *
     * @param deviceId 设备id
     * @param result   结果回调
     */
    void forceUnBindDevice(String deviceId, IResult result);


    /**
     * 开始配网
     * @param ssid
     * @param password
     * @param devcieSSID
     * @param param
     * @param callBack
     */
    void startApconfig(String ssid,String password,String devcieSSID,String param,IApconfigCallBack callBack);
    /**
     * 停止配网
     */
    void stopApconfig(String deviceSSID);

    /**
     * 网络变化
     * @param isConnect
     * @param ssid
     */
    void onNetworkChange(boolean isConnect,String ssid);



    /**
     * 开始发现未配网的设备
     */
    void startDiscoverWifiDevice();

    /**
     * 停止发现未配网的设备
     */
    void stopDiscoverWifiDevice();

    /**
     * 开始发现局域网中的设备
     */
    void startDiscoverNetworkDevice();

    /**
     * 停止发现局域网中的设备
     */
    void stopDiscoverNetworkDevice();



}
