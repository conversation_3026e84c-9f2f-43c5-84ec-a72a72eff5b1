package com.swaiot.aiotlib.device;

import android.os.IBinder;
import android.os.RemoteException;

import com.swaiot.aiotlib.BinderPool;
import com.swaiot.aiotlib.common.base.IResult;
import com.swaiot.aiotlib.device.apconfig.IApconfigDevice;
import com.swaiot.aiotlib.device.discover.IDiscoverDevice;

import org.json.JSONObject;

import java.util.Map;

/**
 * @ClassName: DeviceImpl
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 19:42
 * @Description:
 */
public class DeviceImpl implements IDevice{
    private BinderPool binderPool;
    private String commandId;

    public DeviceImpl(BinderPool binderPool, String commandId) {
        this.binderPool = binderPool;
        this.commandId = commandId;
    }

    /**
     * 获取设备列表，设备列表绑定在家庭下
     *
     * @param familyId 家庭id
     * @param callBack 结果回调
     */
    @Override
    public void getDeviceList(String familyId, IDevice.IDevicesCallBack callBack) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_DEVCIE_LIST, callBack);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_INFO);//获取Binder后使用
        IDeviceInfo info = IDeviceInfo.Stub.asInterface(binder);
        try {
            if(info!=null){
                info.getDeviceList(familyId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 控制指定设备
     *
     * @param deviceId 家庭id
     * @param params   只需要传改变的属性值 如： {"mode": "auto"}
     * @param result   结果回调
     */
    @Override
    public void controlDevice(String deviceId, Map<String, String> params, IDevice.IControlResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_CONTROL_DEVICE, result);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_CONTROL);//获取Binder后使用
        IDeviceControl control = IDeviceControl.Stub.asInterface(binder);
        JSONObject jsonObj = new JSONObject(params);//转化为json格式
        try {
            if(control!=null){
                control.controlDevice(deviceId, jsonObj.toString(), commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void cancelNewDeviceMark(String deviceId, IControlResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_CANCEL_NEW_DEVICE_MARK, result);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_CONTROL);//获取Binder后使用
        IDeviceControl control = IDeviceControl.Stub.asInterface(binder);
        try {
            if(control!=null){
                control.cancelNewDeviceMark(deviceId,commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 绑定设备到指定的家庭id
     *
     * @param deviceId 设备id
     * @param familyId 家庭id
     * @param params   属性值
     * @param result   结果回调
     */
    @Override
    public void bindDevice(String deviceId, String familyId, Map<String, String> params, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_BIND_DEVICE, result);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_MANAGER);//获取Binder后使用
        IDeviceManager manager = IDeviceManager.Stub.asInterface(binder);
        JSONObject jsonObj = new JSONObject(params);//转化为json格式
        try {
            if(manager!=null){
                manager.bindDevice(deviceId, jsonObj.toString(), commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }

    }


    /**
     * 局域网发现时批量获取局域网的设备绑定状态
     *
     * @param params   属性值
     * @param callBack 结果回调
     */
    @Override
    public void getBindStatus(Map<String, String> params, IDevice.IBindStatusCallBack callBack) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_BIND_STATUS, callBack);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_MANAGER);//获取Binder后使用
        IDeviceManager manager = IDeviceManager.Stub.asInterface(binder);
        JSONObject jsonObj = new JSONObject(params);//转化为json格式
        try {
            if(manager!=null){
                manager.getBindStatus(jsonObj.toString(), commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }

    }


    /**
     * 解绑设备
     *
     * @param deviceId 设备id
     * @param result   结果回调
     */
    @Override
    public void unBindDevice(String deviceId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_UNBIND_DEVICE, result);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_MANAGER);//获取Binder后使用
        IDeviceManager manager = IDeviceManager.Stub.asInterface(binder);
        try {
            if(manager!=null){
                manager.unBindDevice(deviceId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }

    }


    /**
     * 强制解绑设备
     *
     * @param deviceId 设备id
     * @param result   结果回调
     */
    @Override
    public void forceUnBindDevice(String deviceId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_FORCE_UNBIND_DEVICE, result);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_MANAGER);//获取Binder后使用
        IDeviceManager manager = IDeviceManager.Stub.asInterface(binder);
        try {
            if(manager!=null){
                manager.forceUnBindDevice(deviceId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startApconfig(String ssid, String password,String devcieSSID,String param,IApconfigCallBack callBack) {
        binderPool.setCallBack(BinderPool.KEY_START_APCONFIG, callBack);
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_APCONFIG);//获取Binder后使用
        IApconfigDevice apconfigDevice = IApconfigDevice.Stub.asInterface(binder);
        try {
            if(apconfigDevice!=null){
                apconfigDevice.startApconfig(ssid,password,devcieSSID,param);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stopApconfig(String deviceSSID) {
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_APCONFIG);//获取Binder后使用
        IApconfigDevice apconfigDevice = IApconfigDevice.Stub.asInterface(binder);
        try {
            if(apconfigDevice!=null){
                apconfigDevice.stopApconfig(deviceSSID);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onNetworkChange(boolean isConnect, String ssid) {
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_APCONFIG);//获取Binder后使用
        IApconfigDevice apconfigDevice = IApconfigDevice.Stub.asInterface(binder);
        try {
            if(apconfigDevice!=null){
                apconfigDevice.onNetworkChange(isConnect,ssid);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startDiscoverWifiDevice() {
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_DISCOVER);//获取Binder后使用
        IDiscoverDevice discoverDevice = IDiscoverDevice.Stub.asInterface(binder);
        try {
            if(discoverDevice!=null){
                discoverDevice.startDiscoverWifiDevice();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stopDiscoverWifiDevice() {
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_DISCOVER);//获取Binder后使用
        IDiscoverDevice discoverDevice = IDiscoverDevice.Stub.asInterface(binder);
        try {
            if(discoverDevice!=null){
                discoverDevice.stopDiscoverWifiDevice();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startDiscoverNetworkDevice() {
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_DISCOVER);//获取Binder后使用
        IDiscoverDevice discoverDevice = IDiscoverDevice.Stub.asInterface(binder);
        try {
            if(discoverDevice!=null){
                discoverDevice.startDiscoverNetworkDevice();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stopDiscoverNetworkDevice() {
        IBinder binder = binderPool.queryBinder(BinderPool.DEVICE_DISCOVER);//获取Binder后使用
        IDiscoverDevice discoverDevice = IDiscoverDevice.Stub.asInterface(binder);
        try {
            if(discoverDevice!=null){
                discoverDevice.stopDiscoverNetworkDevice();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
}
