package com.swaiot.aiotlib;

import android.os.IBinder;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/25
 */
interface IBinderPool {
    IBinder queryBinder(int binderCode);

    void requireResource(String resourceType,String commandId);

    void controlObject(String objectType, String deviceId, String data,String commandId);

    void watch_resources(String resource_list);

    void cancel_watch_resources(String resource_list);

    void operate_stask(String task_type, String operate_type,String task_id,String task_options);

}
