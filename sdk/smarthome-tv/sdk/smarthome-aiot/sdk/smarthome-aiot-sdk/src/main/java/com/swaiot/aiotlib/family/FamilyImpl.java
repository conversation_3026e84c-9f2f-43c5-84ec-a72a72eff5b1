package com.swaiot.aiotlib.family;

import android.os.IBinder;
import android.os.RemoteException;

import com.swaiot.aiotlib.BinderPool;
import com.swaiot.aiotlib.common.base.IResult;

/**
 * @ClassName: FamilyImpl
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 19:43
 * @Description:
 */
public class FamilyImpl implements IFamily {

    private BinderPool binderPool;
    private String commandId;

    public FamilyImpl(BinderPool binderPool, String commandId) {
        this.binderPool = binderPool;
        this.commandId = commandId;
    }

    /**
     * 获取家庭列表，设备列表绑定在家庭下
     *
     * @param callBack 结果回调
     */
    @Override
    public void getFamilyList(IFamily.IFamiliesCallBack callBack) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_FAMILY_LIST, callBack);
        IBinder binder = binderPool.queryBinder(BinderPool.FAMILY_INFO);//获取Binder后使用
        IFamilyInfo familyInfo = IFamilyInfo.Stub.asInterface(binder);
        try {
            if(familyInfo!=null){
                familyInfo.getFamilyList(commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }


    }


    /**
     * 获取家庭设备状态
     *
     * @param familyId 家庭id
     * @param callBack 结果回调
     */
    @Override
    public void getFamilyStatusData(String familyId, IFamily.IFamilyStatusCallBack callBack) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_HOME_STATUS, callBack);
        IBinder binder = binderPool.queryBinder(BinderPool.FAMILY_INFO);//获取Binder后使用
        IFamilyInfo familyInfo = IFamilyInfo.Stub.asInterface(binder);
        try {
            if(familyInfo!=null){
                familyInfo.getFamilyStatusData(familyId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }

    }

    /**
     * 设置当前家庭
     *
     * @param result 结果回调
     */
    @Override
    public void setCurrentFamily(String familyId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_SET_CURRENT_FAMILY, result);
        IBinder binder = binderPool.queryBinder(BinderPool.FAMILY_MANAGER);//获取Binder后使用
        IFamilyManager manager = IFamilyManager.Stub.asInterface(binder);

        try {
            if(manager!=null){
                manager.setCurrentFamily(familyId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 添加家庭
     *
     * @param result 结果回调
     */
    @Override
    public void addFamily(IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_ADD_FAMILY, result);
        IBinder binder = binderPool.queryBinder(BinderPool.FAMILY_MANAGER);//获取Binder后使用
        IFamilyManager manager = IFamilyManager.Stub.asInterface(binder);

        try {
            if(manager!=null){
                manager.addFamily(commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 编辑家庭
     *
     * @param result 结果回调
     */
    @Override
    public void editFamily(String familyId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_EDIT_FAMILY, result);
        IBinder binder = binderPool.queryBinder(BinderPool.FAMILY_MANAGER);//获取Binder后使用
        IFamilyManager manager = IFamilyManager.Stub.asInterface(binder);

        try {
            if(manager!=null){
                manager.editFamily(familyId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 删除家庭
     *
     * @param result 结果回调
     */
    @Override
    public void deleteFamily(String familyId, IResult result) {
        binderPool.setCallBack(BinderPool.KEY_HTTP_DELETE_FAMILY, result);
        IBinder binder = binderPool.queryBinder(BinderPool.FAMILY_MANAGER);//获取Binder后使用
        IFamilyManager manager = IFamilyManager.Stub.asInterface(binder);
        try {
            if(manager!=null){
                manager.deleteFamily(familyId, commandId);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }}
