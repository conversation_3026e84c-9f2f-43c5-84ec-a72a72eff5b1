package com.swaiot.aiotlib.family;

import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.FamilyStatusBean;
import com.swaiot.aiotlib.common.base.IResult;

import java.util.List;

/**
 * @ClassName: IFamily
 * @Author: Awen<PERSON><PERSON>
 * @CreateDate: 2020/5/8 19:40
 * @Description:
 */
public interface IFamily {

    /**
     * 获取设备列表结果回调
     */
    interface IFamiliesCallBack {
        void onFamilies(int code, String msg, List<FamilyBean> list);
    }

    /**
     * 获取家庭状态结果回调
     */
    interface IFamilyStatusCallBack {
        void onFamilyStatus(int code, String msg, List<FamilyStatusBean> list);
    }


    /**
     * 获取家庭列表，设备列表绑定在家庭下
     *
     * @param callBack 结果回调
     */
    void getFamilyList(IFamiliesCallBack callBack);


    /**
     * 获取家庭设备状态
     *
     * @param familyId 家庭id
     * @param callBack 结果回调
     */
    void getFamilyStatusData(String familyId, IFamilyStatusCallBack callBack);


    /**
     * 设置当前家庭
     *
     * @param result 结果回调
     */
    void setCurrentFamily(String familyId, IResult result);

    /**
     * 添加家庭
     *
     * @param result 结果回调
     */
    void addFamily(IResult result);


    /**
     * 编辑家庭
     *
     * @param result 结果回调
     */
    void editFamily(String familyId, IResult result);


    /**
     * 删除家庭
     *
     * @param result 结果回调
     */
    void deleteFamily(String familyId, IResult result);
}
