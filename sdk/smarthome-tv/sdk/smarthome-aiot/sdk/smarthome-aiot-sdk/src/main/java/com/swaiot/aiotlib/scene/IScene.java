package com.swaiot.aiotlib.scene;

import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;
import com.swaiot.aiotlib.common.base.IResult;

import java.util.List;

/**
 * @ClassName: IScene
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 19:41
 * @Description:
 */
public interface IScene {

    /**
     * 获取设备列表结果回调
     */
    interface ISceneCallBack {
        void onScenes(int code, String msg, List<SceneBean> list);
    }


    /**
     * 获取场景列表
     *
     * @param callBack 结果回调
     */
    void getSceneList(ISceneCallBack callBack);


    /**
     * 获取场景列表
     *
     * @param cmd     指令
     * @param sceneId 场景id
     * @param result  结果回调
     */
    void controlScene(String cmd, String sceneId, IResult result);

    /**
     * 取消新场景标志
     * @param scene_id
     */
    void cancelNewSceneMark(String scene_id,IResult result);


    /**
     * 添加场景
     *
     * @param result  结果回调
     */
    void addScene(IResult result);


    /**
     * 编辑场景
     *
     * @param sceneId 场景id
     * @param result  结果回调
     */
    void editScene(String sceneId, IResult result);

    /**
     * 删除场景
     *
     * @param sceneId 场景id
     * @param result  结果回调
     */
    void deleteScene(String sceneId, IResult result);
}
