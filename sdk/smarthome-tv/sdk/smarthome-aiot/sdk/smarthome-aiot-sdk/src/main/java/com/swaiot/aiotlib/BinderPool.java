package com.swaiot.aiotlib;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;
import com.swaiot.aiotlib.device.IDevice;
import com.swaiot.aiotlib.family.IFamily;
import com.swaiot.aiotlib.push.IAIOTPushService;
import com.swaiot.aiotlib.push.IAIOTPushServiceCallback;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.FamilyStatusBean;
import com.swaiot.aiotlib.common.entity.SceneBean;
import com.swaiot.aiotlib.common.entity.BindStatusBean;
import com.swaiot.aiotlib.common.entity.ControlResult;
import com.swaiot.aiotlib.common.entity.DeviceBean;
import com.swaiot.aiotlib.common.base.IResult;
import com.swaiot.aiotlib.common.base.ReceiveListener;
import com.swaiot.aiotlib.scene.IScene;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;


public class BinderPool {

    private static final String TAG = AiotLibSDK.TAG;

    public static final String KEY_HTTP_DEVCIE_LIST = "device_list";
    public static final String KEY_HTTP_CONTROL_DEVICE = "control_device";
    public static final String KEY_HTTP_CANCEL_NEW_DEVICE_MARK = "unmark_new_device";
    public static final String KEY_HTTP_BIND_DEVICE = "bind_device";
    public static final String KEY_HTTP_BIND_STATUS = "bind_status";
    public static final String KEY_HTTP_UNBIND_DEVICE = "unbind_device";
    public static final String KEY_HTTP_FORCE_UNBIND_DEVICE = "force_unbind_device";


    public static final String KEY_HTTP_FAMILY_LIST = "family_list";
    public static final String KEY_HTTP_HOME_STATUS = "home_status";
    public static final String KEY_HTTP_SET_CURRENT_FAMILY = "set_current_family";
    public static final String KEY_HTTP_ADD_FAMILY = "add_family";
    public static final String KEY_HTTP_EDIT_FAMILY = "edit_family";
    public static final String KEY_HTTP_DELETE_FAMILY = "delete_family";

    public static final String KEY_HTTP_SCENE_LIST = "scene_list";
    public static final String KEY_HTTP_CONTROL_SENCE = "control_sence";
    public static final String KEY_HTTP_CANCEL_NEW_SCENE_MARK = "unmark_new_scene";
    public static final String KEY_HTTP_SCENE_MANAGER = "scene_manager";
    public static final String KEY_HTTP_EDIT_SCENE = "edit_scene";
    public static final String KEY_HTTP_DELETE_SCENE = "delete_scene";


    public static final String KEY_HTTP_CURRENT_FAMILY = "current_family";
    public static final String KEY_HTTP_DEVICE_NOTIFY = "device_notify";

    public static final String KEY_START_APCONFIG = "start_apconfig";


    public static final String KEY_HANDEL_DEVICE = "device";
    public static final String LOCAL_CONTROL = "local_control";


    public static final int DEVICE_INFO = 1;
    public static final int DEVICE_CONTROL = 2;
    public static final int DEVICE_MANAGER = 3;

    public static final int FAMILY_INFO = 4;
    public static final int FAMILY_MANAGER = 5;

    public static final int SCENE_CONTROL = 6;
    public static final int SCENE_INFO = 7;
    public static final int SCENE_MANAGER = 8;
    public static final int AIOT_PUSH = 9;

    public static final int DEVICE_APCONFIG = 10;
    public static final int DEVICE_DISCOVER = 11;

    public static final int ACCOUNT_INFO = 12;

    private enum BIND_STATUS {
        IDLE, BINDING, BINDED
    }
    private BIND_STATUS binded = BIND_STATUS.IDLE;
    private Context mContext;
    private String mBindKey;
    private AiotLibSDK.Platform mPlatform;
    private IBinderPool mBinderPool;
    private IAIOTPushService iCallBackManager;
    private CountDownLatch mCountDownLatch = new CountDownLatch(1);
    //用于http请求的结果回调
    private HashMap<String, Object> httpListener;
    private AiotLibSDK.InitListener mInitListener;
    //用于处理服务器主动推送的消息回调
    private HashMap<String, ReceiveListener> pushListener;
    private ServiceConnection conn;
    private int mReconnectCount;
    private static final int RECONNECT_MAX_COUNT = 30;//重连最大次数


    private IAIOTPushServiceCallback mProxyCallback = new IAIOTPushServiceCallback.Stub() {

        @Override
        public void onInit(String data) throws RemoteException {
            for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                //String key = entry.getKey();
                ReceiveListener listener = entry.getValue();
                if (listener != null) {
                    listener.onInit(data);
                }
            }
        }

        @Override
        public void onApconfigProgress(int progress, int total, String extra) throws RemoteException {
            if (httpListener.containsKey(KEY_START_APCONFIG)) {
                Object value = httpListener.get(KEY_START_APCONFIG);
                if (value instanceof IDevice.IApconfigCallBack) {
                    IDevice.IApconfigCallBack callBack = (IDevice.IApconfigCallBack) value;
                    callBack.onApconfigProgress(progress,total,extra);
                }
            }
        }

        @Override
        public void onApconfigOk(String result) throws RemoteException {
            if (httpListener.containsKey(KEY_START_APCONFIG)) {
                Object value = httpListener.get(KEY_START_APCONFIG);
                if (value instanceof IDevice.IApconfigCallBack) {
                    IDevice.IApconfigCallBack callBack = (IDevice.IApconfigCallBack) value;
                    callBack.onApconfigOk(result);
                }
            }
        }

        @Override
        public void onApconfigFail(int code, String erro) throws RemoteException {
            if (httpListener.containsKey(KEY_START_APCONFIG)) {
                Object value = httpListener.get(KEY_START_APCONFIG);
                if (value instanceof IDevice.IApconfigCallBack) {
                    IDevice.IApconfigCallBack callBack = (IDevice.IApconfigCallBack) value;
                    callBack.onApconfigFail(code,erro);
                }
            }
        }

        @Override
        public void onApconfigConnectNetFail(String wifiInfo) throws RemoteException {
            if (httpListener.containsKey(KEY_START_APCONFIG)) {
                Object value = httpListener.get(KEY_START_APCONFIG);
                if (value instanceof IDevice.IApconfigCallBack) {
                    IDevice.IApconfigCallBack callBack = (IDevice.IApconfigCallBack) value;
                    callBack.onApconfigConnectNetFail(wifiInfo);
                }
            }
        }

        @Override
        public void onAccountChange() throws RemoteException {
            for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                //String key = entry.getKey();
                ReceiveListener listener = entry.getValue();
                if (listener != null) {
                    listener.onAccountChange();
                }
            }
        }

        @Override
        public void onReceiveData(String event, String data) throws RemoteException {
            Log.d(TAG, "onReceiveData() called with: event = [" + event + "], data = [" + data + "]");
            for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                //String key = entry.getKey();
                ReceiveListener listener = entry.getValue();
                if (listener != null) {
                    listener.onReceive(event, data);
                }
            }
        }

        @Override
        public void onHandleDataCallback(String object_type, String device_id, String data) throws RemoteException {
            Log.d(TAG, "onHandleDataCallback() called with: object_type = [" + object_type + "], device_id = [" + device_id + "], data = [" + data + "]");
            for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                //String key = entry.getKey();
                ReceiveListener listener = entry.getValue();
                if (listener != null) {
                    listener.onHandleDataCallback(object_type, device_id, data);
                }
            }
        }

        @Override
        public void onDiscoverWifiDevice(String result) throws RemoteException {
            Log.d(TAG,"onDiscoverWifiDevice() called with: deviceList = [" +result + "]");
            if(result!=null){
                try{
                    List<DiscoverWifiDevice> deviceInfoList = JSONArray.parseArray(result,DiscoverWifiDevice.class);
                    for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                        //String key = entry.getKey();
                        ReceiveListener listener = entry.getValue();
                        if (listener != null) {
                            listener.onDiscoverWifiDevice(deviceInfoList);
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onDiscoverNetworkDevice(String result) throws RemoteException {
            if(result!=null){
                try{
                    Log.d(TAG,"onDiscoverNetworkDevice() called with: deviceList = [" +result + "]");
                    List<DiscoverNetworkDevice> deviceInfoList = JSONArray.parseArray(result,DiscoverNetworkDevice.class);
                    for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                        //String key = entry.getKey();
                        ReceiveListener listener = entry.getValue();
                        if (listener != null) {
                            listener.onDiscoverNetworkDevice(deviceInfoList);
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onSpecialVoiceHandle(String cmd) throws RemoteException {
            Log.d(TAG, "onSpecialVoiceHandle() called with: cmd = [" + cmd + "]");
            for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                //String key = entry.getKey();
                ReceiveListener listener = entry.getValue();
                if (listener != null) {
                    listener.onSpecialVoiceHandle(cmd);
                }
            }
        }



        @Override
        public void onHttpCallBack(int code, String msg, String data, String resource_type, String commandId) throws RemoteException {
            Log.d(TAG, "onHttpCallBack() called with: code = [" + code + "], msg = [" + msg + "], resource_type = [" + resource_type + "], commandId = [" + commandId + "]");
            for (Map.Entry<String, ReceiveListener> entry : pushListener.entrySet()) {
                String key = entry.getKey();
                ReceiveListener listener = entry.getValue();
                if (listener != null) {
                    listener.onHttpCallBack(code, msg, data, resource_type, commandId);
                }
            }
            if(TextUtils.isEmpty(commandId)){//只有指定请求才会返回
                return;
            }
            switch (resource_type) {
                case KEY_HTTP_DEVCIE_LIST:   //获取设备列表，设备列表绑定在家庭下
                    if (httpListener.containsKey(KEY_HTTP_DEVCIE_LIST)) {
                        Object value = httpListener.get(KEY_HTTP_DEVCIE_LIST);
                        if (value instanceof IDevice.IDevicesCallBack) {
                            IDevice.IDevicesCallBack callBack = (IDevice.IDevicesCallBack) value;
                            if(code == 0){
                                List<DeviceBean> list = JSONObject.parseArray(data,DeviceBean.class);
                                callBack.onDevices(code, msg, list);
                            }else{
                                callBack.onDevices(code, msg, null);
                            }
                        }
                    }
                    break;
                case KEY_HTTP_CONTROL_DEVICE: //控制指定设备
                    if (httpListener.containsKey(KEY_HTTP_CONTROL_DEVICE)) {
                        Object value = httpListener.get(KEY_HTTP_CONTROL_DEVICE);
                        if (value instanceof IDevice.IControlResult) {
                            IDevice.IControlResult callBack = (IDevice.IControlResult) value;
                            if(code == 0){
                                ControlResult result = JSONObject.parseObject(data,ControlResult.class);
                                callBack.onControlResult(code, msg, result);
                            }else{
                                callBack.onControlResult(code, msg, null);
                            }
                        }
                    }
                    break;
                case KEY_HTTP_CANCEL_NEW_DEVICE_MARK://取消新设备标志
                    if (httpListener.containsKey(KEY_HTTP_CANCEL_NEW_DEVICE_MARK)) {
                        Object value = httpListener.get(KEY_HTTP_CANCEL_NEW_DEVICE_MARK);
                        if (value instanceof IDevice.IControlResult) {
                            ControlResult result = JSONObject.parseObject(data,ControlResult.class);
                            IDevice.IControlResult callBack = (IDevice.IControlResult) value;
                            callBack.onControlResult(code, msg, result);
                        }
                    }
                    break;
                case KEY_HTTP_BIND_DEVICE:  //绑定设备到指定的家庭id
                    if (httpListener.containsKey(KEY_HTTP_BIND_DEVICE)) {
                        Object value = httpListener.get(KEY_HTTP_BIND_DEVICE);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_BIND_STATUS:  //局域网发现时批量获取局域网的设备绑定状态
                    if (httpListener.containsKey(KEY_HTTP_BIND_STATUS)) {
                        Object value = httpListener.get(KEY_HTTP_BIND_STATUS);
                        if (value instanceof IDevice.IBindStatusCallBack) {
                            IDevice.IBindStatusCallBack callBack = (IDevice.IBindStatusCallBack) value;
                            if(code == 0){
                                List<BindStatusBean> list = JSONObject.parseArray(data,BindStatusBean.class);
                                callBack.onBindStatus(code, msg, list);
                            }else{
                                callBack.onBindStatus(code, msg, null);
                            }
                        }
                    }
                    break;
                case KEY_HTTP_UNBIND_DEVICE:  //解绑设备
                    if (httpListener.containsKey(KEY_HTTP_UNBIND_DEVICE)) {
                        Object value = httpListener.get(KEY_HTTP_UNBIND_DEVICE);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_FORCE_UNBIND_DEVICE: //强制解绑设备
                    if (httpListener.containsKey(KEY_HTTP_FORCE_UNBIND_DEVICE)) {
                        Object value = httpListener.get(KEY_HTTP_FORCE_UNBIND_DEVICE);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_FAMILY_LIST:  //获取家庭列表，设备列表绑定在家庭下
                    if (httpListener.containsKey(KEY_HTTP_FAMILY_LIST)) {
                        Object value = httpListener.get(KEY_HTTP_FAMILY_LIST);
                        if (value instanceof IFamily.IFamiliesCallBack) {
                            IFamily.IFamiliesCallBack callBack = (IFamily.IFamiliesCallBack) value;
                            if(code == 0){
                                List<FamilyBean> list = JSONObject.parseArray(data,FamilyBean.class);
                                callBack.onFamilies(code, msg, list);
                            }else{
                                callBack.onFamilies(code, msg, null);
                            }
                        }
                    }
                    break;
                case KEY_HTTP_HOME_STATUS:   //获取家庭设备状态
                    if (httpListener.containsKey(KEY_HTTP_HOME_STATUS)) {
                        Object value = httpListener.get(KEY_HTTP_HOME_STATUS);
                        if (value instanceof IFamily.IFamilyStatusCallBack) {
                            IFamily.IFamilyStatusCallBack callBack = (IFamily.IFamilyStatusCallBack) value;
                            if(code == 0){
                                List<FamilyStatusBean> list = JSONObject.parseArray(data,FamilyStatusBean.class);
                                callBack.onFamilyStatus(code, msg, list);
                            }else{
                                callBack.onFamilyStatus(code, msg, null);
                            }
                        }
                    }
                    break;
                case KEY_HTTP_SET_CURRENT_FAMILY:  //设置当前家庭
                    if (httpListener.containsKey(KEY_HTTP_SET_CURRENT_FAMILY)) {
                        Object value = httpListener.get(KEY_HTTP_SET_CURRENT_FAMILY);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_ADD_FAMILY:  //添加家庭
                    if (httpListener.containsKey(KEY_HTTP_ADD_FAMILY)) {
                        Object value = httpListener.get(KEY_HTTP_ADD_FAMILY);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_EDIT_FAMILY: //编辑家庭
                    if (httpListener.containsKey(KEY_HTTP_EDIT_FAMILY)) {
                        Object value = httpListener.get(KEY_HTTP_EDIT_FAMILY);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_DELETE_FAMILY: //删除家庭
                    if (httpListener.containsKey(KEY_HTTP_DELETE_FAMILY)) {
                        Object value = httpListener.get(KEY_HTTP_DELETE_FAMILY);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_SCENE_LIST:  //获取场景列表
                    if (httpListener.containsKey(KEY_HTTP_SCENE_LIST)) {
                        Object value = httpListener.get(KEY_HTTP_SCENE_LIST);
                        if (value instanceof IScene.ISceneCallBack) {
                            IScene.ISceneCallBack callBack = (IScene.ISceneCallBack) value;
                            if(code == 0){
                                List<SceneBean> list = JSONObject.parseArray(data,SceneBean.class);
                                callBack.onScenes(code, msg, list);
                            }else{
                                callBack.onScenes(code, msg, null);
                            }
                        }
                    }
                    break;
                case KEY_HTTP_CONTROL_SENCE:  //控制场景
                    if (httpListener.containsKey(KEY_HTTP_CONTROL_SENCE)) {
                        Object value = httpListener.get(KEY_HTTP_CONTROL_SENCE);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_CANCEL_NEW_SCENE_MARK://取消新设备标志
                    if (httpListener.containsKey(KEY_HTTP_CANCEL_NEW_SCENE_MARK)) {
                        Object value = httpListener.get(KEY_HTTP_CANCEL_NEW_SCENE_MARK);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_SCENE_MANAGER:  //添加场景
                    if (httpListener.containsKey(KEY_HTTP_SCENE_MANAGER)) {
                        Object value = httpListener.get(KEY_HTTP_SCENE_MANAGER);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_EDIT_SCENE:  //编辑场景
                    if (httpListener.containsKey(KEY_HTTP_EDIT_SCENE)) {
                        Object value = httpListener.get(KEY_HTTP_EDIT_SCENE);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                case KEY_HTTP_DELETE_SCENE: //删除场景
                    if (httpListener.containsKey(KEY_HTTP_DELETE_SCENE)) {
                        Object value = httpListener.get(KEY_HTTP_DELETE_SCENE);
                        if (value instanceof IResult) {
                            IResult callBack = (IResult) value;
                            callBack.onResult(code, msg);
                        }
                    }
                    break;
                default:
                    break;
            }

        }
    };


    public BinderPool(Context context, AiotLibSDK.Platform platform,String bindKey) {
        mContext = context.getApplicationContext();
        mBindKey = bindKey;
        mPlatform = platform;
        httpListener = new HashMap<>();
        pushListener = new HashMap<>();
    }

    public boolean isBind() {
        return binded == BIND_STATUS.BINDED;
    }


    /**
     * 设置http请求通用回调
     *
     * @param key 键
     * @param o   回调
     */
    public void setCallBack(String key, Object o) {
        httpListener.put(key, o);
    }


    /**
     * 用户服务器主动推送消息的回调
     *
     * @param key          键
     * @param httpListener 回调
     */
    public void setReceiveListener(String key, ReceiveListener httpListener) {
        pushListener.put(key, httpListener);
    }

    public synchronized void bindPoolService(final AiotLibSDK.InitListener initListener) {
        Log.d(TAG, "bindPoolService");
        mInitListener = initListener;
        if (binded == BIND_STATUS.IDLE) {
            binded = BIND_STATUS.BINDING;
            new Thread(new Runnable() {
                @Override
                public void run() {
                    String BIND_ACTION = "com.swaiot.aiotlib.service";
                    String BIND_PACKAGE = mContext.getPackageName();
                    Log.d(TAG, "包名："+ BIND_PACKAGE);
                    Intent intent = new Intent();
                    intent.setAction(BIND_ACTION);
                    intent.setPackage(BIND_PACKAGE);
                    conn = new ServiceConnection() {
                        @Override
                        public void onServiceConnected(ComponentName name, IBinder service) {
                            Log.d(TAG, "Service is connected!!! " );
                            mBinderPool = IBinderPool.Stub.asInterface(service);
                            try {
                                IBinder binder = mBinderPool.queryBinder(AIOT_PUSH);//获取Binder后使用
                                iCallBackManager = IAIOTPushService.Stub.asInterface(binder);
                                iCallBackManager.registerCallback(mBindKey, mProxyCallback);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            binded = BIND_STATUS.BINDED;
                            mCountDownLatch.countDown();
                            if(initListener!=null){
                                initListener.success();
                            }
                        }

                        @Override
                        public void onServiceDisconnected(ComponentName name) {
                            Log.d(TAG, "Service is disconnected!!! ");
                            mBinderPool = null;
                            binded = BIND_STATUS.IDLE;
                            if(initListener!=null){
                                initListener.fail();
                            }
                            if(!isStopBindService() && mPlatform != AiotLibSDK.Platform.PHONE){
                                bindPoolService(initListener);
                            }
                        }
                    };
                    mContext.bindService(intent, conn, Context.BIND_AUTO_CREATE);
                    try {
                        mCountDownLatch.await(30, TimeUnit.SECONDS);
                        if(binded!= BIND_STATUS.BINDED&&initListener!=null){
                            initListener.fail();
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
            Log.d(TAG, "bind Pool Service！");

        } else if (binded == BIND_STATUS.BINDING) {
            //do nothing
        } else if (binded == BIND_STATUS.BINDED) {
            initListener.success();
        }
    }

    /**
     * 重新绑定服务
     */
    public void reBindService(){
        if(mInitListener!=null){
            bindPoolService(mInitListener);
        }
    }


    //获取Binder
    public IBinder queryBinder(int binderCode) {
        IBinder binder = null;
        try {
            if (null != mBinderPool) {
                binder = mBinderPool.queryBinder(binderCode);
            }else{
                reBindService();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return binder;
    }


    public IBinderPool getBinderPool() {
        return mBinderPool;
    }

    /**
     * 是否停止绑定服务
     * @return
     */
    private boolean isStopBindService(){
        if(mReconnectCount > RECONNECT_MAX_COUNT){
            return true;
        }
        mReconnectCount++;
        return false;
    }

    /**
     * sdk销毁
     */
    public void destroy() {
        if (mContext != null) {
            if (iCallBackManager != null) {
                try {
                    iCallBackManager.unregisterCallback(mContext.getPackageName(), mProxyCallback);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            if (binded == BIND_STATUS.BINDED) {
                binded = BIND_STATUS.IDLE;
                mContext.unbindService(conn);
            }
        }
    }


}
