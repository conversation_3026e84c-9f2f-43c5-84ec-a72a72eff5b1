package com.swaiot.aiotlib.common.entity;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: DeviceBean
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2020/3/23 16:58
 * @Description: 设备bean
 */
public class DeviceBean implements Serializable {
    /**
     * device_id : 512b8f6b020ffd90dfb89606d8950ce7
     * device_type_id : 82
     * device_icon : https://tv.doubimeizhi.com/images/tv/device/82_logo.png
     * device_icon_for_ir : https://....device/82_logo.png
     * device_name : 鞋柜三门
     * online_status : 0
     * device_position : 客厅
     * device_status_des : 剩余xx分钟
     * is_virtual : true
     * acees_type : 1
     * status_show : {"data_field":"POW_S","current":0,"values":{"0":"OFF","1":"ON"}}
     */
    public String device_id;
    public String gateway_id;//红外宝设备id
    public String familyId;
    public String device_type_id;
    public String device_icon;
    public String device_icon_for_ir;
    public String product_type_id;
    public String product_type;
    public String product_brand;
    public String product_brand_id;
    public String product_model;
    public String module_chip;
    public String device_name;
    public String features;
    public String did;
    public int online_status;
    public String device_position;
    public String device_status_desc;
    public boolean is_virtual;
    public boolean is_new;
    public int acess_type;//0：wifi 1：zigbee, 2: ble 3: 红外设备
    public String report_status;
    public List<String> voice_tips;//设备语音引导句
    public String detail_layout;//设备详情ui控件信息,控制面板
    /**
     * data_field : POW_S
     * current : 0
     * values : {"0":"OFF","1":"ON"}
     */
    public StatusShowBean status_show;

    /**
     * data_field : POW_S
     * current : 0
     * values : {"0":"PAUSE","1":"START"}
     */
    public StatusShowBean pasue_start;


    public static class StatusShowBean implements Serializable{
        public String data_field;
        public String values;
    }

    public static String POW_STATUS_ON = "1";
    public static String POW_STATUS_OFF = "0";

}
