package com.swaiot.aiotlib;

import android.content.Context;
import android.os.RemoteException;
import android.util.Log;

import com.swaiot.aiotlib.account.AccountImpl;
import com.swaiot.aiotlib.account.IAccountImpl;
import com.swaiot.aiotlib.common.base.ReceiveListener;
import com.swaiot.aiotlib.device.DeviceImpl;
import com.swaiot.aiotlib.device.IDevice;
import com.swaiot.aiotlib.family.FamilyImpl;
import com.swaiot.aiotlib.family.IFamily;
import com.swaiot.aiotlib.scene.IScene;
import com.swaiot.aiotlib.scene.SceneImpl;


public class AiotLibSDK {
    public static final String TAG = "smarthome-aiot-sdk";
    private static volatile AiotLibSDK instance;
    private BinderPool binderPool;
    private String commandId;
    private IDevice mDeviceImpl;
    private IFamily mFamilyImpl;
    private IScene mSceneImpl;
    private IAccountImpl mAccountImpl;
    /**
     * 平台
     */
    public enum Platform{
        TV,
        PAD,
        PHONE
    }
    /**
     * SDK初始化结果监听器
     */
    public interface InitListener {
        void success();

        void fail();
    }

    public static AiotLibSDK getDefault() {
        if (null == instance) {
            synchronized (AiotLibSDK.class) {
                if (null == instance) {
                    instance = new AiotLibSDK();
                }
            }
        }
        return instance;
    }
    /**
     * sdk初始化
     */
    public void init(Context context,Platform platform,InitListener listener) {
        commandId = context.getPackageName() + "-" + context.getApplicationInfo().uid;
        binderPool = new BinderPool(context,platform,commandId);
        mDeviceImpl = new DeviceImpl(binderPool,commandId);
        mFamilyImpl = new FamilyImpl(binderPool,commandId);
        mSceneImpl = new SceneImpl(binderPool,commandId);
        mAccountImpl = new AccountImpl(binderPool,commandId);
        binderPool.bindPoolService(listener);
    }

    /**
     * 接受 server 主动推送的消息
     *
     * @param listener 监听器
     */
    public void setReceiveListener(ReceiveListener listener) {
        checkInit();
        binderPool.setReceiveListener(commandId, listener);
    }

    public IDevice getDeviceImpl() {
        checkInit();
        return mDeviceImpl;
    }

    public IFamily getFamilyImp() {
        checkInit();
        return mFamilyImpl;
    }

    public IScene getSceneImp() {
        checkInit();
        return mSceneImpl;
    }

    public IAccountImpl getAccountImp() {
        checkInit();
        return mAccountImpl;
    }

    public void requireResource(String resourceType) {
        if(!checkInit()){
            return;
        }
        try {
            binderPool.getBinderPool().requireResource(resourceType, commandId);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void controlObject(String objectType, String deviceId, String data) {
        if(!checkInit()){
            return;
        }
        try {
            binderPool.getBinderPool().controlObject(objectType, deviceId, data, commandId);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void watch_resources(String resource_list){
        if(!checkInit()){
            return;
        }
        try {
            binderPool.getBinderPool().watch_resources(resource_list);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

   public void cancel_watch_resources(String resource_list){
       if(!checkInit()){
           return;
       }
       try {
           binderPool.getBinderPool().cancel_watch_resources(resource_list);
       } catch (RemoteException e) {
           e.printStackTrace();
       }
   }

   public void operate_stask(String task_type, String operate_type,String task_id,String task_options){
       if(!checkInit()){
           return;
       }
       try {
           binderPool.getBinderPool().operate_stask(task_type,operate_type,task_id,task_options);
       } catch (RemoteException e) {
           e.printStackTrace();
       }
    }

    private boolean checkInit() {
        if (binderPool == null) {
            Log.d(TAG, "AiotLibSDK not init!");
            return false;
        }
        if (!binderPool.isBind()) {
            Log.d(TAG, "AiotLibSDK bind service fail!");
            return false;
        }
        return true;
    }

    /**
     * sdk销毁
     */
    public void destroy() {
        if(binderPool!=null){
            binderPool.destroy();
        }
    }


}
