package com.swaiot.aiotlib.common.base;

import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import java.util.List;

public interface ReceiveListener {

    void onInit(String data);

    void onAccountChange();

    void onReceive(String event, String data);

    void onHandleDataCallback(String object_type, String device_id, String data);

    void onHttpCallBack(int code, String msg, String data, String resource_type, String commandId);

    /**
     * 发现未配网的设备
     * @param deviceList
     */
    void onDiscoverWifiDevice(List<DiscoverWifiDevice> deviceList);

    /**
     * 发现局域网中的未绑定的设备
     * @param deviceList
     */
    void onDiscoverNetworkDevice(List<DiscoverNetworkDevice> deviceList);


    void onSpecialVoiceHandle(String cmd);
}
