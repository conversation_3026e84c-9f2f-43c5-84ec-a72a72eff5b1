package com.swaiot.aiotlib.common.entity;

public class DiscoverDeviceDetail {
    /**
     * brand : 创维
     * product : 全时AI精灵
     * model : IPA01
     * number : 47
     */
    private String brand;
    private String product;
    private String model;
    private String key;
    private int number;
    private String img_url;

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getImg_url() {
        return img_url;
    }

    public void setImg_url(String img_url) {
        this.img_url = img_url;
    }

    @Override
    public String toString() {
        return getBrand()+","+getProduct() + "," +getModel()+ "," + getNumber() + "," + getKey();
    }
}
