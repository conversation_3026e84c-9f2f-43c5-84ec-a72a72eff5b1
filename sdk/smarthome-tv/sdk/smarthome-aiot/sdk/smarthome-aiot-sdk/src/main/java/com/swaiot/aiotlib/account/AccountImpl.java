package com.swaiot.aiotlib.account;

import android.os.IBinder;
import android.os.RemoteException;

import com.swaiot.aiotlib.BinderPool;

/**
 * @ClassName: AccountImpl
 * @Author: AwenZeng
 * @CreateDate: 2020/8/25 18:40
 * @Description:
 */
public class AccountImpl implements IAccountImpl{

    private BinderPool binderPool;
    private String commandId;

    public AccountImpl(BinderPool binderPool, String commandId) {
        this.binderPool = binderPool;
        this.commandId = commandId;
    }

    @Override
    public void onInitAccountInfo(String accountInfo) {
        IBinder binder = binderPool.queryBinder(BinderPool.ACCOUNT_INFO);//获取Binder后使用
        IAccountInfo info = IAccountInfo.Stub.asInterface(binder);
        try {
            if(info!=null){
                info.onInitAccountInfo(accountInfo);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onAccountChange(boolean isLogin, String accountInfo) {
        IBinder binder = binderPool.queryBinder(BinderPool.ACCOUNT_INFO);//获取Binder后使用
        IAccountInfo info = IAccountInfo.Stub.asInterface(binder);
        try {
            if(info!=null){
                info.onAccountChange(isLogin,accountInfo);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
}
