package com.swaiot.aiotlib.common.entity;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/8
 */
public class WifiInfo {
    /**
     * The network name.
     */
    public String SSID;

    /**
     * The address of the access point.
     */
    public String BSSID;

    /**
     * The detected signal level in dBm, also known as the RSSI.
     *
     * <p>Use {@link android.net.wifi.WifiManager#calculateSignalLevel} to convert this number into
     * an absolute signal level which can be displayed to a user.
     */
    public int level;
    /**
     * The primary 20 MHz frequency (in MHz) of the channel over which the client is communicating
     * with the access point.
     */
    public int frequency;
}
