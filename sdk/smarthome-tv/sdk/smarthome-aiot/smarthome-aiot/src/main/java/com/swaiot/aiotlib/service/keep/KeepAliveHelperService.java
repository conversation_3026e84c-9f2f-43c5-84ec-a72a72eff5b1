package com.swaiot.aiotlib.service.keep;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.os.Build;

/**
 * @ClassName: KeepAliveHelperService
 * @Author: AwenZeng
 * @CreateDate: 2020/5/19 14:05
 * @Description:
 */
public class KeepAliveHelperService{

    public static void keep(Service service,int id) {
        try{
            if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
                service.startForeground(id, getNotification(service));
            }else{
                service.startForeground(1, getNotification(service));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private static Notification getNotification(Service service) {
        Notification.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager nm = (NotificationManager) service.getSystemService(Context.NOTIFICATION_SERVICE);
            //数字是随便写的“40”，
            nm.createNotificationChannel(new NotificationChannel("10", "App Service", NotificationManager.IMPORTANCE_NONE));
            builder = new Notification.Builder(service, "10");
        } else {
            builder = new Notification.Builder(service);
        }
        builder.setPriority(Notification.PRIORITY_MAX)
                .setWhen(System.currentTimeMillis())
                .setDefaults(Notification.DEFAULT_ALL);
        Notification notification = builder.build();
        notification.flags = Notification.FLAG_AUTO_CANCEL;
        return notification;
    }


    public static void stopForeground(Service service){
        service.stopForeground(true);
    }
}
