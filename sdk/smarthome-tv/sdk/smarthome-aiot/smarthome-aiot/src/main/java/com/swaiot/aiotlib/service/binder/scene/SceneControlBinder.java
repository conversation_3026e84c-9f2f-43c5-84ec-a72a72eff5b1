package com.swaiot.aiotlib.service.binder.scene;

import android.os.RemoteException;

import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.scene.ISceneControl;
import com.swaiot.lib.SkyAIOTContract;

/**
 * @ClassName: SceneControlBinder
 * @Author: Awen<PERSON><PERSON>
 * @CreateDate: 2020/5/8 18:53
 * @Description:
 */
public class SceneControlBinder extends ISceneControl.Stub {
    @Override
    public boolean controlScene(String cmd, String scene_id,String commandId) throws RemoteException {
        SkyAIOTContract.control_object(AiotConstants.KEY_HTTP_CONTROL_SENCE,  EmptyUtils.handleNullString(scene_id), EmptyUtils.handleNullString(cmd), EmptyUtils.handleNullString(commandId));
        return false;
    }

    @Override
    public void cancelNewSceneMark(String scene_id, String commandId) throws RemoteException {
        SkyAIOTContract.control_object(AiotConstants.KEY_CANCEL_NEW_SCENE_TAG, EmptyUtils.handleNullString(scene_id), "", EmptyUtils.handleNullString(commandId));
    }
}
