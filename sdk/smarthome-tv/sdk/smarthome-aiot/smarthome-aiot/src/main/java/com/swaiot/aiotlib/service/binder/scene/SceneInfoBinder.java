package com.swaiot.aiotlib.service.binder.scene;

import android.os.RemoteException;

import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.scene.ISceneInfo;
import com.swaiot.lib.SkyAIOTContract;

/**
 * @ClassName: SceneInfoBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 18:53
 * @Description:
 */
public class SceneInfoBinder extends ISceneInfo.Stub {
    @Override
    public String getSceneList(String commandId) throws RemoteException {
        LogUtil.androidLog("getSceneList:"+ commandId);
        SkyAIOTContract.require_resource(AiotConstants.KEY_HTTP_SCENE_LIST,  EmptyUtils.handleNullString(commandId),(byte)1);
        return null;
    }
}
