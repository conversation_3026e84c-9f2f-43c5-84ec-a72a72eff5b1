package com.swaiot.aiotlib.service.model;

import android.os.RemoteException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.isupatches.wisefy.callbacks.SearchForSSIDsCallbacks;
import com.swaiot.aiotlib.common.model.AiotAppData;
import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.device.apconfig.module.WifiModule;
import com.swaiot.aiotlib.service.AiotService;
import com.swaiot.aiotlib.service.binder.push.AIOTPushServiceBinder;
import com.swaiot.lib.SkyAIOTCallback;
import com.swaiot.lib.SkyAIOTContract;

import java.util.List;

/**
 * @ClassName: AIOTServicePushCallback
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2020/4/26 19:20
 * @Description: rust公共库相关回调
 */
public class SkyAiotLibCallback implements SkyAIOTCallback{
    private WifiModule mWifiUtils;
    private static AIOTPushServiceBinder mAiotPushServiceBinder;
    private SearchForSSIDsCallbacks mSsidCallback = new SearchForSSIDsCallbacks() {
        @Override
        public void retrievedSSIDs(List<String> list) {
            LogUtil.androidLog( "retrievedSSIDs() called with: list = [" + list + "]");
            final String[] arr = new String[list.size()];
            for (int i = 0; i < list.size(); i++) {
                arr[i] = list.get(i);
            }
            SkyAIOTContract.on_app_status(AiotConstants.KEY_SSID_LIST,JSONObject.toJSONString(arr));
        }

        @Override
        public void noSSIDsFound() {
            LogUtil.androidLog(  "noSSIDsFound() called");
            final String[] arr= {};
            SkyAIOTContract.on_app_status(AiotConstants.KEY_SSID_LIST,JSONObject.toJSONString(arr));
        }

        @Override
        public void wisefyFailure(int i) {
            LogUtil.androidLog( "wisefyFailure() called with: i = [" + i + "]");
        }
    };
    public SkyAiotLibCallback(AIOTPushServiceBinder aiotPushServiceBinder) {
        mAiotPushServiceBinder = aiotPushServiceBinder;
        mWifiUtils = new WifiModule(AiotAppData.getInstance().getContext(),mSsidCallback);
    }

    @Override
    public void on_object_updated(String object_type, String device_id, String data) {
        LogUtil.androidLog("on_object_updated() called with: object_type = " + object_type + ", device_id = " + device_id + ", data = " + data );
        if (EmptyUtils.isNotEmpty(mAiotPushServiceBinder)) {
            try {
                mAiotPushServiceBinder.onHandleDataCallback(object_type, device_id, data);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void on_resource_data(int code, String msg, String resource_type, String data, String commandId) {
        LogUtil.androidLog("on_resource_data() called with: code = " + code + ", msg = " + msg + ", resource_type = " + resource_type + ", data = " + data + ", commandId = " + commandId);
        if (EmptyUtils.isNotEmpty(mAiotPushServiceBinder)) {
            try {
                if(resource_type.equals(AiotConstants.KEY_DISCOVERY_DEVICE_LIST)){
                    mAiotPushServiceBinder.onDiscoverNetworkDevice(data);
                }else{
                    if(AiotAppData.getInstance().isStartApconfig()){
                        return;
                    }
                    mAiotPushServiceBinder.onHttpCallBack(code, msg, data, resource_type, commandId);
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void on_control_result(int code, String msg, String object_type, String object_id, String data, String commandId) {
        LogUtil.androidLog("on_control_result() called with: code = " + code + ", msg = " + msg + ", object_type = " + object_type + ", object_id = " + object_id + ", operate = " + data + ", commandId = " + commandId);
        if (EmptyUtils.isNotEmpty(mAiotPushServiceBinder)) {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("device_id", object_id);
                jsonObject.put("status", data);
                mAiotPushServiceBinder.onHttpCallBack(code, msg, jsonObject.toJSONString(), object_type, commandId);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void require_app_operate(String require_type, String operate_data) {
        LogUtil.androidLog("require_app_operate() called with: require_type = [" + require_type + "], operate_data = [" + operate_data + "]");
        if(EmptyUtils.isNotEmpty(require_type)){
            switch (require_type){
                case AiotConstants.KEY_SSID_LIST:
                    mWifiUtils.searchForSSIDs(AiotAppData.getInstance().getContext(), "^(SKYLINK|skynj-)([0-9]+M[a-z0-9]{4}|[a-z0-9]{12})$");
                    break;
                case AiotConstants.KEY_NETWORK_INFO:
                    break;
                case AiotConstants.KEY_CONNECT_WIFI:
                    if(EmptyUtils.isEmpty(operate_data))
                        return;
                    JSONObject jsonObject = JSONObject.parseObject(operate_data);
                    String password = jsonObject.getString("password");
                    String ssid = jsonObject.getString("ssid");
                    mWifiUtils.connectWiFi(ssid, password, 20000, new WifiModule.WifiConnectCallback() {
                        @Override
                        public void onConnectFail(int code, String msg) {
                            LogUtil.androidLog( "onConnectFail() called with: code = [" + code + "], msg = [" + msg + "]");
                            try{
                                AiotService.mAiotPushServiceBinder.onApconfigConnectNetFail(jsonObject.toJSONString());
                            }catch (RemoteException e){
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public void onConnectOk() {
                            LogUtil.androidLog( "onConnectOk() called");
                        }
                    });
                    break;
            }
        }
    }

    @Override
    public void on_task_status(String task_type, String task_id, String task_status, String extra) {
        LogUtil.androidLog("on_task_status() called with: task_type = [" + task_type + "], task_id = [" + task_id + "], task_status = [" + task_status + "], extra = [" + extra + "]");
        if(task_type.equals(AiotConstants.KEY_SKY_WIFI_CONFIG)){
            switch (task_status){
                case AiotConstants.KEY_APCONFIG_PROGRESS:
                    if (EmptyUtils.isNotEmpty(mAiotPushServiceBinder)&EmptyUtils.isNotEmpty(extra)) {
                        try {
                            JSONObject extraObject = JSON.parseObject(extra);
                            int progress = extraObject.getInteger("progress");
                            int total = extraObject.getInteger("total");
                            mAiotPushServiceBinder.onApconfigProgress(progress,total,extra);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }

                    break;
                case AiotConstants.KEY_APCONFIG_SUCCESS:
                    if (EmptyUtils.isNotEmpty(mAiotPushServiceBinder)) {
                        try {
                            mAiotPushServiceBinder.onApconfigOk(extra);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                case AiotConstants.KEY_APCONFIG_FAIL:
                    if (EmptyUtils.isNotEmpty(mAiotPushServiceBinder)) {
                        try {
                            JSONObject jsonObject = JSONObject.parseObject(extra);
                            int code = jsonObject.getInteger("code");
                            String msg = jsonObject.getString("msg");
                            mAiotPushServiceBinder.onApconfigFail(code,msg);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
            }
        }
    }
}
