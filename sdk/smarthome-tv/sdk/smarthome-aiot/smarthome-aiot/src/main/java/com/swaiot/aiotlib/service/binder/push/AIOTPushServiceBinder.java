package com.swaiot.aiotlib.service.binder.push;

import android.content.Intent;
import android.os.Bundle;
import android.os.RemoteCallbackList;
import android.os.RemoteException;

import com.swaiot.aiotlib.AIOTLib;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.common.model.AiotAppData;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.common.util.ThreadManager;
import com.swaiot.aiotlib.push.IAIOTPushService;
import com.swaiot.aiotlib.push.IAIOTPushServiceCallback;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: AIOTPushServiceBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/4/1 11:01
 * @Description: AIDL推送接口实现
 */
public class AIOTPushServiceBinder extends IAIOTPushService.Stub implements IAIOTPushServiceCallback {

    private RemoteCallbackList<IAIOTPushServiceCallback> mCallbacks;
    private Map<String, IAIOTPushServiceCallback> mKeyMap;

    public AIOTPushServiceBinder() {
        mCallbacks = new RemoteCallbackList<>();
        mKeyMap = new HashMap<>();
    }

    @Override
    public void registerCallback(String key, IAIOTPushServiceCallback callback) throws RemoteException {
        if (EmptyUtils.isEmpty(key) || EmptyUtils.isEmpty(callback)) {
            return;
        }
        if (EmptyUtils.isNotEmpty(mCallbacks)) {
            if (mKeyMap.containsKey(key)) {
                mCallbacks.unregister(mKeyMap.get(key));
                mKeyMap.remove(key);
                mCallbacks.register(callback);
                mKeyMap.put(key, callback);
            } else {
                mCallbacks.register(callback);
                mKeyMap.put(key, callback);
            }
        } else {
            mCallbacks.register(callback);
            mKeyMap.put(key, callback);
        }
    }

    @Override
    public void unregisterCallback(String key, IAIOTPushServiceCallback callback) throws RemoteException {
        if (EmptyUtils.isEmpty(key) || EmptyUtils.isEmpty(callback)) {
            return;
        }
        if (EmptyUtils.isNotEmpty(mCallbacks) && EmptyUtils.isNotEmpty(mKeyMap)) {
            mCallbacks.unregister(callback);
            mKeyMap.remove(key);
        }
    }

    @Override
    public void onInit(String data) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int N = mCallbacks.beginBroadcast();
                        for (int i = 0; i < N; i++) {
                            mCallbacks.getBroadcastItem(i).onInit(data);
                        }
                        LogUtil.androidLog("onInit ：push-connect-count" + N);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onAccountChange() throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int N = mCallbacks.beginBroadcast();
                        for (int i = 0; i < N; i++) {
                            mCallbacks.getBroadcastItem(i).onAccountChange();
                        }
                        LogUtil.androidLog("on_account_change ：push-connect-count" + N);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onReceiveData(String event, String data) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int N = mCallbacks.beginBroadcast();
                        for (int i = 0; i < N; i++) {
                            mCallbacks.getBroadcastItem(i).onReceiveData(event, data);
                        }
                        LogUtil.androidLog("on_receive_data push-connect-count：" + N);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onHandleDataCallback(String object_type, String device_id, String data) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onHandleDataCallback(object_type, device_id, data);
                        }
                        LogUtil.androidLog("on_object_updated push-connect-count：" + n);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
        try{
            if (object_type.equals("device") && AIOTLib.getDefault().getPlatform() == AiotLibSDK.Platform.TV) {
                sendDeviceStatusToHomePlugin("DEVICE_STATUS",data);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    /**
     * 发送数据给主页插件的小维智联
     *
     * @param data
     */
    private void sendDeviceStatusToHomePlugin(String key,String data) {
        Intent intent = new Intent();
        intent.setPackage("com.ccos.tvlauncher");
        intent.setAction("ccos.action.HOME.SERVICE");
        intent.putExtra("doWhat", "deliver_plugin_msg");//固定格式
        Bundle bundle = new Bundle();
        bundle.putString("pkg", "com.skyworth.smarthome_tv");//插件包名，必须，通过包名传给对应插件
        bundle.putString("PUSH_TYPE", key);//省略，添加自己需要的其他数据
        bundle.putString("PUSH_DATA", data);//省略，添加自己需要的其他数据
        intent.putExtra("msg", bundle);//key固定msg，bundle把数据携带过去
        AiotAppData.getInstance().getContext().startService(intent);
    }

    @Override
    public void onHttpCallBack(int code, String msg, String data, String resource_type, String commandId) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            IAIOTPushServiceCallback callback = mCallbacks.getBroadcastItem(i);
                            if (EmptyUtils.isEmpty(commandId)) {
                                callback.onHttpCallBack(code, msg, data, resource_type, commandId);
                            } else if (isSend(commandId, callback)) {
                                callback.onHttpCallBack(code, msg, data, resource_type, commandId);
                                break;
                            }
                        }
                        LogUtil.androidLog("on_resource_changed push-connect-count：" + commandId + n);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
        try{
            if(EmptyUtils.isEmpty(commandId)&&resource_type.equals("device_list")&&AIOTLib.getDefault().getPlatform() == AiotLibSDK.Platform.TV){
                sendDeviceStatusToHomePlugin("DEVICE_LIST",data);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void onApconfigProgress(int progress, int total, String extra) throws RemoteException {

        //开始配网，设置配网标志
        if(progress<total){
            AiotAppData.getInstance().setStartApconfig(true);
        }else{
            AiotAppData.getInstance().setStartApconfig(false);
        }

        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onApconfigProgress(progress, total,extra);
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }


    @Override
    public void onApconfigOk(String result) throws RemoteException {
        AiotAppData.getInstance().setStartApconfig(false);
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onApconfigOk(result);
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onApconfigFail(int code, String erro) throws RemoteException {
        AiotAppData.getInstance().setStartApconfig(false);
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onApconfigFail(code, erro);
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onApconfigConnectNetFail(String wifiInfo) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onApconfigConnectNetFail(wifiInfo);
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onDiscoverWifiDevice(String result) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks){
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onDiscoverWifiDevice(result);
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onDiscoverNetworkDevice(String result) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onDiscoverNetworkDevice(result);
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    @Override
    public void onSpecialVoiceHandle(String cmd) throws RemoteException {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                synchronized (mCallbacks) {
                    try {
                        final int n = mCallbacks.beginBroadcast();
                        for (int i = 0; i < n; i++) {
                            mCallbacks.getBroadcastItem(i).onSpecialVoiceHandle(cmd);
                        }
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    } finally {
                        mCallbacks.finishBroadcast();
                    }
                }
            }
        });
    }

    public void destroy() {
        if (EmptyUtils.isNotEmpty(mCallbacks)) {
            mCallbacks.kill();
        }
        if (EmptyUtils.isNotEmpty(mKeyMap)) {
            mKeyMap.clear();
        }
    }


    private boolean isSend(String key, IAIOTPushServiceCallback callback) {
        if (mKeyMap.containsKey(key)) {
            IAIOTPushServiceCallback value = mKeyMap.get(key);
            if (value != null && value.equals(callback)) {
                return true;
            }
        }
        return false;
    }
}
