package com.swaiot.aiotlib.service.model;

import com.swaiot.aiotlib.common.bean.SsePushBean;
import com.swaiot.aiotlib.service.binder.push.AIOTPushServiceBinder;

/**
 * @ClassName: IAIOTServiceModel
 * @Author: AwenZeng
 * @CreateDate: 2020/4/3 14:24
 * @Description:
 */
public interface IAIOTServiceModel {

     void initSID();

     void initRustLib();

     void onInitScreenId(String screenId);

     void registerIotSse();

     void unRegisterIotSse();

     void deviceListForSync();

     void registerSSE(boolean isNeedUnRegister);

     void registerNetworkReceiver();

     void unRegisterNetworkReceiver();

     void noticeRustLibAccountChange();

     void noticeRustLibSsePushMessage(SsePushBean ssePushBean);

     void onDestroy();

}
