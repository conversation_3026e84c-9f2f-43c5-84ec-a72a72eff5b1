package com.swaiot.aiotlib.service.model;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;

import com.swaiot.aiotlib.common.model.AiotAppData;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;

import static android.content.Context.WIFI_SERVICE;


/**
 * @ClassName: NetworkModel
 * @Author: AwenZeng
 * @CreateDate: 2020/5/16 16:42
 * @Description: 网络模块
 */
public class NetworkModel implements INetworkModel {
    private Context mContext;
    private NetworkChangeListener mListerner;
    private BroadcastReceiver netReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action.equals(ConnectivityManager.CONNECTIVITY_ACTION)) {
                LogUtil.androidLog("网络变化：" + isNetworkConnected());
                if(EmptyUtils.isNotEmpty(mListerner)){
                    mListerner.onNetworkChanged(isNetworkConnected());
                }
            }
        }
    };

    public NetworkModel(Context context) {
        this.mContext = context;
        registerNetReceiver();
    }

    @Override
    public void setNetworkChangeListerner(NetworkChangeListener mListerner) {
        this.mListerner = mListerner;
    }

    @Override
    public void registerNetReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        mContext.registerReceiver(netReceiver, intentFilter);
    }

    @Override
    public void unRegisterNetReceiver() {
        try {
            mContext.unregisterReceiver(netReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 网络是否连接
     * @return
     */
    @Override
    public boolean isNetworkConnected(){
        ConnectivityManager connectivityManager = (ConnectivityManager) AiotAppData.getInstance().getContext().getSystemService(
                Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
        if(EmptyUtils.isNotEmpty(networkInfo)){
            return networkInfo.isConnected();
        }
        return false;
    }

    @Override
    public String getCurrentWifiSSID() {
        WifiManager wifiManager = (WifiManager) AiotAppData.getInstance().getContext().getApplicationContext().getSystemService(WIFI_SERVICE);
        if (wifiManager == null) {
            return null;
        }
        WifiInfo info = wifiManager.getConnectionInfo();
        String ssid = info.getSSID();
        if (ssid.startsWith("\"") && ssid.endsWith("\"")) {
            ssid = ssid.substring(1, ssid.length() - 1);
        }
        return ssid;
    }

    public interface NetworkChangeListener{
        void onNetworkChanged(boolean isConnected);
    }
}
