package com.skyworth.smarthome.home.smartdevice.scene;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.dialog.SenceDialog;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.home.base.BaseSmartItemView;
import com.skyworth.smarthome.home.base.BaseSmartListView;
import com.skyworth.smarthome.service.model.IAIOTModel;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/18
 */
public class SceneListView extends BaseSmartListView<SceneBean> {

    private int mMaxShowActonCount;
    private int mModeIconResId;
    private String mModeTitle;

    private TextView mTitle;
    private SenceDialog mSenceDialog;
    private LinearLayout scenelayout;
    private View modeIcon;

    public SceneListView(Context context) {
        this(context, "", R.drawable.automatic, 3);
    }

    public SceneListView(Context context, String modeTitle, int modeIconResId, int maxShowActonCount) {
        super(context);
        this.mModeTitle = modeTitle;
        this.mModeIconResId = modeIconResId;
        this.mMaxShowActonCount = maxShowActonCount;

        ITEM_H = Util.Div(150 + 10);
        CONTENT_TOP_MARGIN = Util.Div(85);
    }

    @Override
    public void addTitle() {
        super.addTitle();

        scenelayout = new LinearLayout(getContext());
        scenelayout.setGravity(Gravity.CENTER);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addView(scenelayout, layoutParams);

        modeIcon = ImageLoader.getLoader().getView(getContext());
        modeIcon.setBackgroundResource(mModeIconResId);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.Div(30), Util.Div(30));
        params.rightMargin=Util.Div(10);
        scenelayout.addView(modeIcon);

        mTitle = new TextView(getContext());
        mTitle.setTextColor(Color.WHITE);
        mTitle.setTextSize(Util.Dpi(30));
        mTitle.getPaint().setFakeBoldText(true);
        mTitle.setText(mModeTitle);
        scenelayout.addView(mTitle);
    }

    @Override
    public void refreshItem(SceneBean updateData) {
        super.refreshItem(updateData);
        for (int i = 0; i < mDatas.size(); i++) {
            SceneBean bean = mDatas.get(i);
                if (bean.scene_id.equals(updateData.scene_id)) {
                    ((SceneItemView) getChildAt(i + 1)).refreshUI(updateData,i+1);
                    invalidate();
                    break;
                }
            }

    }

    @Override
    protected BaseSmartItemView<SceneBean> getItemView() {
        return new SceneItemView(getContext(), mMaxShowActonCount);
    }

    @Override
    protected BaseSmartItemView<String> getAddItemView() {
        return new AddSceneItemView(getContext());
    }

    @Override
    public void onItemClick(int position) {
        super.onItemClick(position);
        final SceneBean sceneBean = mDatas.get(position);
        if (EmptyUtils.isNotEmpty(sceneBean)) {
            if (sceneBean.is_auto) {
                setSceneSwitch(sceneBean.scene_id, !sceneBean.isOpen);
            } else {
                controlScene(sceneBean.scene_id);
            }
            if (sceneBean.is_new) {
                cancelNewSceneTag(sceneBean.scene_id);
            }
        }
    }

    @Override
    public void resetFocus() {
        View v = getChildAt(mLastFocusPos);
        if (v != null) {
            v.requestFocus();
        }
    }

    private void setSceneSwitch(final String scene_id, final boolean isEnable) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                SmartBaseData result = ISmartHomeModel.INSTANCE.setSceneAutoSwitch(scene_id, isEnable);
                if (result != null && result.code.equals("0")) {
                    ThreadManager.getInstance().uiThread(new Runnable() {
                        @Override
                        public void run() {
                            SceneBean sceneListBean = null;
                            for (int i = 0; i < mDatas.size(); i++) {
                                if (mDatas.get(i).scene_id.equals(scene_id)) {
                                    sceneListBean = mDatas.get(i);
                                    sceneListBean.isOpen = isEnable;
                                    mDatas.set(i, sceneListBean);
                                    refreshItem(sceneListBean);
                                    break;
                                }
                            }
                            if (sceneListBean != null) {
                                showToast("场景" + (sceneListBean.isOpen ? "启用" : "停用") + "成功");
                            }
                        }
                    });
                } else {
                    showToast("场景开关失败");
                }
            }
        });
    }

    private void controlScene(final String scene_id) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                SmartBaseData result = ISmartHomeModel.INSTANCE.controlScene("start", scene_id);
                if (result != null) {
                    if (result.code.equals("0")) {//成功
                        showToast("发送成功，执行场景中");
                        reportControlData("success");
                    } else {
                        showToast("执行失败");
                        reportControlData("fail");
                    }
                }
            }
        });
    }

    private void cancelNewSceneTag(final String sceneID) {
        IAIOTModel.INSTANCE.cancelNewSceneMark(sceneID, null);
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                SceneBean sceneBean;
                for (int i = 0; i < mDatas.size(); i++) {
                    if (mDatas.get(i).scene_id.equals(sceneID)) {
                        sceneBean = mDatas.get(i);
                        sceneBean.is_new = false;
                        mDatas.set(i, sceneBean);
                        refreshItem(sceneBean);
                        break;
                    }
                }
            }
        });
    }

    private void reportControlData(String result) {
        Map<String, String> map = new HashMap<>();
        map.put("scene_type", "手动场景");
        map.put("control_result", result);
        LogSDK.submit(LogSDK.EVENT_ID_SCENE_CONTROL, map);
    }

    private void showToast(final String msg) {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                XToast.showToast(getContext(), msg);
            }
        });
    }

    @Override
    public void onAddItemClick() {
        super.onAddItemClick();

        if (mSenceDialog == null) {
            mSenceDialog = new SenceDialog(getContext());
        }
        mSenceDialog.show();
//        XToast.showToast(SmartHomeTvLib.getContext(),"功能优化中，敬请期待");
    }
}
